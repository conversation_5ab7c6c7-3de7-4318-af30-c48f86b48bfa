import { useState } from 'react';
import { useStoreId } from '@/src/hooks/use-store-id';
import { GetServiceRequest } from '@/src/api/types/service.types';
import { serviceService } from '@/src/api/services/dashboard/product/service.service';
import { ErrorHandlerService } from '@/src/api/services/error-handler.service';
import { StorageService } from 'nextjs-api-lib';

export const useService = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const storeId = useStoreId();
  const partnerId = StorageService.get('partnerId') as string | null;

  const getService = async (
    skip: number,
    limit: number,
    itemsType: 'Product' | 'Service',
    categoryId?: string,
    subCategoryId?: string,
    search: string = ''
  ) => {
    try {
      setLoading(true);
      setError(null);
      
      const params: GetServiceRequest = {
        shopId: storeId,
        partnerId,
        itemsType,
        categoryId: categoryId || null,
        subCategoryId: subCategoryId || null,
        search,
        skip,
        limit
      };

      const response = await serviceService.getService(params);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const createService = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await serviceService.createService({
        ...data,
        partnerId
      });
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const updateService = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      const response = await serviceService.updateService({
        ...data,
        partnerId
      });
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const updateServiceImage = async (serviceId: string, imageFile: File) => {
    try {
      setLoading(true);
      setError(null);
      const response = await serviceService.updateImageService(serviceId, imageFile);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const deleteService = async (itemsCode: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await serviceService.deleteService(itemsCode);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  const getServiceDetail = async (serviceId: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await serviceService.getServiceDetail(serviceId);
      return response;
    } catch (err: any) {
      const errorResponse = ErrorHandlerService.handle(err, {
        showSnackbar: true,
        logError: true
      });
      setError(errorResponse.detail);
      throw errorResponse;
    } finally {
      setLoading(false);
    }
  };

  return {
    getService,
    createService,
    updateService,
    updateServiceImage,
    deleteService,
    getServiceDetail,
    loading,
    error,
  };
}; 