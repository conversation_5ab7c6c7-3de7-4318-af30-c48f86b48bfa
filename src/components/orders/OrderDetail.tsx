import React, { useEffect, useState } from "react";
import DashboardLayout from "@/src/layouts/dashboard";
import {
  Avatar,
  Box,
  Button,
  Card,
  CircularProgress,
  Divider,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import {
  getOrderStatusLabel,
  getOrderStatusPayLabel,
  getStatusDeliveryLabel,
  getTransportStatusLabel,
  OrderProductPayStatusWithBg,
  OrderProductTransportStatusWithBg,
} from "@/src/utils/order/order-helper";
import PageTitleWithBackBtn from "@/src/layouts/dashboard/page-title-with-back-btn/page-title";
import dayjs from "dayjs";
import OrderDetailItemsBox from "./OrderDetailItemsBox";
import OrderDetailPaymentSummary from "./OrderDetailPaymentSummary";
import PersonIcon from "@mui/icons-material/Person";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useOrder } from "@/src/api/hooks/order/use-order";
import useSnackbar from "@/src/hooks/use-snackbar";
import Link from "next/link";
import { paths } from "@/src/paths";
import {
  EventNote,
  FileDownload,
  GetApp,
  LocalShipping,
  PermContactCalendar,
  Receipt,
} from "@mui/icons-material";
import { useInvoice } from "@/src/api/hooks/invoice/invoice";
import { InvoiceQueryParams } from "@/src/api/services/invoice/invoice.service";
const LeftColumn = ({ children }) => {
  return (
    <Grid size={{ xs: 12, md: 8 }}>
      <Box>{children}</Box>
    </Grid>
  );
};

const RightColumn = ({ children }) => {
  return (
    <Grid size={{ xs: 12, md: 4 }}>
      <Box>{children}</Box>
    </Grid>
  );
};

export type UpdateActionType = "StatusDelivery" | "UpdateNotes" | "CancelOrder" | "StatusPay";

export interface InvoiceHistoryDto {
  shopId: string;
  orderNo: string;
  provider: string;
  transactionId: string;
  status: string;
  templateCode: string;
  invoiceSeries: string;
  reservationCode: string;
  invoiceType: string;
  invoiceNo: string;
  sellerTaxCode: string;
}
const getInvoiceStatusConfig = (status: string) => {
  switch (status) {
    case "Success":
      return {
        color: "rgb(34, 154, 22)",
        backgroundColor: "rgba(84, 214, 44, 0.16)",
        label: "Thành công",
      };
    case "Failed":
      return {
        color: "rgb(183, 33, 54)",
        backgroundColor: "rgba(255, 72, 66, 0.16)",
        label: "Thất bại",
      };
    case "Processing":
      return {
        color: "rgb(255, 171, 0)",
        backgroundColor: "rgba(255, 171, 0, 0.16)",
        label: "Đang thực hiện",
      };
    case "Cancelled":
      return {
        color: "rgb(99, 115, 129)",
        backgroundColor: "rgba(145, 158, 171, 0.16)",
        label: "Đã huỷ",
      };
    default:
      return {
        color: "rgb(99, 115, 129)",
        backgroundColor: "rgba(145, 158, 171, 0.16)",
        label: "Không xác định",
      };
  }
};

function getVietnameseActionName(action) {
  switch (action) {
    case "DraftInvoice":
      return "Lập hóa đơn nháp";
    case "IssueInvoice":
      return "Phát hành hóa đơn";
    case "AdjustInvoice":
      return "Điều chỉnh hóa đơn";
    case "ReplaceInvoice":
      return "Thay thế hóa đơn";
    case "CancelInvoice":
      return "Xoá bỏ hóa đơn";
    case "PreviewDraftInvoice":
      return "Xem trước hóa đơn nháp";
    case "IssueInvoiceHsmSignature":
      return "Phát hành bằng HSM";
    case "IssueInvoiceUsbTokenSignature":
      return "Phát hành bằng USB Token";
    case "IssueInvoiceCloudCaSignature":
      return "Phát hành bằng Cloud CA";
    default:
      return "Không xác định";
  }
}

export default function OrderDetail({ orderData }) {
  const [order, setOrder] = useState(orderData);
  const [printing, setPrinting] = useState(false);
  const { getListHistoryInvoice, downloadFilePdfInvoice } = useInvoice();
  const [listHistoryInvoice, setListHistoryInvoice] = useState<InvoiceHistoryDto[]>([]);
  const storeId = useStoreId();
  const snackbar = useSnackbar();
  const [totalCount, setTotalCount] = useState<number>(0);
  const { cancelOrder, updateNotes, completeShippingItems, getOrder, updatePaidOrder } = useOrder();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const handleClickUpdateStatusOrder = async (action: UpdateActionType) => {
    const data = {
      shopId: storeId,
      orderId: orderData.orderId,
      updateAction: action,
    };
    const response = await completeShippingItems(data);
    if (response?.data) {
      const reponseOrder = await getOrder({ orderId: orderData.orderId, shopId: orderData.shopId });
      if (reponseOrder?.data) {
        setOrder(reponseOrder?.data);
        fetchListHistoryInvoice();
        snackbar.success("Cập nhật thông tin đơn hàng thành công");
      }
    }
  };

  const handleClickCancelOrder = async () => {
    const data = {
      shopId: storeId,
      orderId: orderData.orderId,
      updateAction: "CancelOrder",
    };
    const response = await cancelOrder(data);
    if (response?.data) {
      const reponseOrder = await getOrder({ orderId: orderData.orderId, shopId: orderData.shopId });
      if (reponseOrder?.data) {
        setOrder(reponseOrder?.data);
        snackbar.success("Cập nhật thông tin đơn hàng thành công");
      }
    }
  };

  const handleClickSaveNotes = async (notes) => {
    const data = {
      shopId: storeId,
      orderId: orderData.orderId,
      updateAction: "UpdateNotes",
      notes,
    };
    const response = await updateNotes(data);
    if (response?.data) {
      // const reponseOrder = await getOrder({ orderId: orderData.orderId, shopId: orderData.shopId });
      snackbar.success("Cập nhật thông tin đơn hàng thành công");
    }
  };

  const handleClickPaid = async () => {
    const data = {
      shopId: storeId,
      orderId: orderData.orderId,
      updateAction: "StatusPay",
    };
    const response = await updatePaidOrder(data);
    if (response?.data) {
      setOrder(response?.data);
      fetchListHistoryInvoice();
      snackbar.success("Cập nhật thông tin đơn hàng thành công");
    }
  };

  const handleClickDownloadTransportOrder = async () => {
    setPrinting(true);
    try {
      const url = order.transportOrderLabel;
      if (!url) {
        console.warn("Không có URL label để tải.");
        return;
      }

      // Lấy tên file từ URL (phần sau cùng)
      const fileName = url.split("/").pop() || `don-${order.transportOrderId}.pdf`;

      // Tải bằng fetch → tạo blob
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error("Tải file thất bại.");
      }

      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);

      // Tạo link và tải
      const link = document.createElement("a");
      link.href = blobUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(blobUrl);
    } catch (error) {
      console.error("Lỗi khi tải đơn vận chuyển:", error);
    } finally {
      setPrinting(false);
    }
  };
  useEffect(() => {
    setOrder(orderData);
  }, [orderData]);

  const fetchListHistoryInvoice = async () => {
    const data: InvoiceQueryParams = {
      ShopId: storeId,
      OrderNo: order?.orderNo,
      Paging: {
        NameType: "Created",
        SortType: "asc",
        Name: "Created",
        Sort: "asc",
        PageIndex: page,
        PageSize: rowsPerPage,
      },
    };
    const res = await getListHistoryInvoice(data);
    if (res && res?.status === 200) {
      if (Array.isArray(res?.data?.data?.result) && res?.data?.data?.result.length > 0) {
        setListHistoryInvoice(res?.data?.data?.result || []);
        setTotalCount(res?.data?.data?.total || 0);
      }
    }
  };
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  useEffect(() => {
    fetchListHistoryInvoice();
  }, [page, rowsPerPage, order?.orderNo]);

  const handleExportInvoice = async (invoice: InvoiceHistoryDto) => {
    try {
      const res = await downloadFilePdfInvoice(invoice.invoiceNo);
      if (res?.data) {
        const fileUrl = res.data?.data?.link;
        const fileName = fileUrl.split("/").pop() || `invoice-${invoice.invoiceNo}.pdf`;

        const response = await fetch(fileUrl);
        if (!response.ok) {
        }
        const blob = await response.blob();
        const downloadUrl = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = downloadUrl;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();

        document.body.removeChild(link);
        URL.revokeObjectURL(downloadUrl);
        snackbar.success("Tải xuống hoá đơn thành công");
      }
    } catch (error) {
      console.error("Error downloading invoice:", error);
      snackbar.error("Có lỗi xảy ra khi tải hoá đơn");
    }
  };
  return (
    <DashboardLayout>
      <Box sx={{ paddingRight: 4, paddingLeft: 4, paddingTop: 2, paddingBottom: 4 }}>
        <Typography variant="h5" gutterBottom marginBottom={2}>
          Chi tiết đơn hàng
        </Typography>
        <Box display="flex" gap={2} alignItems="center">
          <PageTitleWithBackBtn
            title={order?.orderNo}
            backPath={paths.orders.list}
            sx={{ mb: 0 }}
          />
          <Box>
            <OrderProductTransportStatusWithBg status={order?.statusTransport} />
          </Box>
          <Box>
            <OrderProductPayStatusWithBg status={order?.statusPay} />
          </Box>
        </Box>
        <Typography variant="subtitle2" color="text.secondary" marginBottom={2}>
          {dayjs(order?.created).format("DD/MM/YYYY HH:mm")}
        </Typography>
        <Grid container spacing={2}>
          <LeftColumn>
            <Card sx={{ p: 2 }}>
              <OrderDetailItemsBox
                order={order}
                handleClickUpdateStatusOrder={handleClickUpdateStatusOrder}
                handleClickCancelOrder={handleClickCancelOrder}
              />
            </Card>

            <Card sx={{ p: 2, marginTop: 3 }}>
              <OrderDetailPaymentSummary order={order} handleClickPaid={handleClickPaid} />
            </Card>
            {/* <NotesSection
              onSave={handleClickSaveNotes}
              notes={order?.notes}
              statusOrder={order?.statusOrder}
            /> */}
            <Card sx={{ p: 2, marginTop: 3 }}>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                Lịch sử hoá đơn
              </Typography>
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ width: "20px" }}>STT</TableCell>
                      <TableCell>Loại hoá đơn</TableCell>
                      <TableCell>Mã hoá đơn</TableCell>
                      <TableCell>Nhà cung cấp</TableCell>
                      <TableCell>Trạng thái</TableCell>
                      {/* <TableCell>Mã số thuế người bán</TableCell> */}
                      <TableCell>Quản lý</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {listHistoryInvoice.length > 0 ? (
                      listHistoryInvoice.map((invoice, index) => (
                        <TableRow key={invoice.invoiceNo}>
                          <TableCell>{index + 1}</TableCell>
                          <TableCell>{getVietnameseActionName(invoice.invoiceType)}</TableCell>
                          <TableCell>{invoice.invoiceNo}</TableCell>
                          <TableCell>{invoice.provider}</TableCell>
                          <TableCell>
                            <Box
                              sx={{
                                display: "inline-block",
                                px: 1.5,
                                py: 0.5,
                                borderRadius: 1,
                                bgcolor: getInvoiceStatusConfig(invoice.status).backgroundColor,
                                color: getInvoiceStatusConfig(invoice.status).color,
                                fontSize: "0.875rem",
                                fontWeight: 500,
                              }}
                            >
                              {getInvoiceStatusConfig(invoice.status).label}
                            </Box>
                          </TableCell>
                          {/* <TableCell>{invoice.sellerTaxCode}</TableCell> */}
                          <TableCell>
                            {invoice?.invoiceNo && (
                              <Tooltip title="Xuất hoá đơn">
                                <IconButton
                                  size="small"
                                  onClick={() => handleExportInvoice(invoice)}
                                  sx={{
                                    color: "primary.main",
                                    "&:hover": {
                                      bgcolor: "primary.lighter",
                                    },
                                  }}
                                >
                                  <FileDownload fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            )}
                          </TableCell>{" "}
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={12} align="center" sx={{ py: 3 }}>
                          <Typography variant="body2" color="text.secondary">
                            Không có dữ liệu
                          </Typography>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
              <TablePagination
                component="div"
                count={totalCount}
                page={page}
                onPageChange={handleChangePage}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                labelRowsPerPage="Số hàng mỗi trang:"
                labelDisplayedRows={({ from, to, count }) => `${from}-${to} của ${totalCount}`}
                rowsPerPageOptions={[10, 25, 50]}
              />
            </Card>
          </LeftColumn>

          <RightColumn>
            <Card sx={{ p: 2 }}>
              <Box>
                <Box sx={{ display: "flex" }}>
                  <PermContactCalendar sx={{ color: "rgb(201, 91, 91)", mr: 2, fontSize: 28 }} />
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 700,
                      mb: 1.5,
                      pb: 1,
                      borderBottom: "1px solid #f0f0f0",
                      width: "100%",
                    }}
                  >
                    Thông tin khách hàng
                  </Typography>
                </Box>
                {order?.creator?.userId ? (
                  <Link
                    href={paths.customers.detail.replace(":id", order?.creator?.userId)}
                    target="_blank"
                  >
                    <Box
                      sx={{
                        backgroundColor: "neutral.50",
                        padding: 2,
                        borderRadius: 1,
                        marginLeft: 5,
                      }}
                      display="flex"
                      justifyContent="space-between"
                      alignItems="center"
                    >
                      <Box display="flex" gap={1} alignItems="center">
                        <Box
                          sx={{
                            height: 30,
                            width: 30,
                            borderRadius: "50%",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                            backgroundColor: "neutral.300",
                          }}
                        >
                          <PersonIcon color="action" />
                        </Box>
                        <Typography>{order?.creator?.fullName || "Khách hàng vãng lai"}</Typography>
                      </Box>
                      <NavigateNextIcon />
                    </Box>
                  </Link>
                ) : (
                  <Box
                    sx={{ backgroundColor: "neutral.50", padding: 2, borderRadius: 1 }}
                    display="flex"
                    alignItems="center"
                    gap={1}
                  >
                    <Box
                      sx={{
                        height: 30,
                        width: 30,
                        borderRadius: "50%",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        backgroundColor: "neutral.300",
                      }}
                    >
                      <PersonIcon color="action" />
                    </Box>
                    <Typography>{order?.creator?.fullName || "Khách hàng vãng lai"}</Typography>
                  </Box>
                )}
                <Box>
                  <Typography sx={{ marginLeft: 5 }} marginTop={1}>
                    {order?.creator?.phoneNumber}
                  </Typography>
                </Box>
              </Box>
            </Card>
            <Card
              sx={{
                mb: 2,
                borderRadius: 1,
                boxShadow: "0 4px 12px rgba(0,0,0,0.05)",
                transition: "box-shadow 0.3s ease-in-out",
                // "&:hover": {
                //   boxShadow: "0 8px 24px rgba(0,0,0,0.12)",
                // },
                overflow: "visible",
              }}
            >
              <Box sx={{ p: 2, mt: 2.5 }}>
                <Box sx={{ display: "flex", alignItems: "flex-start" }}>
                  <LocalShipping sx={{ color: "#1976d2", mr: 2, fontSize: 28 }} />
                  <Box sx={{ width: "100%" }}>
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 700,
                        mb: 1.5,
                        pb: 1,
                        borderBottom: "1px solid #f0f0f0",
                      }}
                    >
                      Phương thức giao hàng
                    </Typography>

                    <Typography
                      sx={{
                        color: "#1976d2",
                        fontWeight: 600,
                        mb: 2,
                      }}
                    >
                      {getStatusDeliveryLabel(order?.statusDelivery, order?.transportService)}
                    </Typography>

                    {order?.statusDelivery === "ExpressDelivery" && (
                      <Box>
                        <Typography sx={{ fontWeight: 600 }}>
                          {order?.userShippingAddress?.fullName}
                        </Typography>

                        <Typography sx={{ color: "#1976d2", my: 0.5 }}>
                          {order?.userShippingAddress?.phoneNumber}
                        </Typography>

                        <Typography sx={{ color: "#555", fontSize: "0.95rem" }}>
                          {[
                            order?.userShippingAddress?.address,
                            order?.userShippingAddress?.wardName,
                            order?.userShippingAddress?.districtName,
                            order?.userShippingAddress?.provinceName,
                          ]
                            .filter(Boolean)
                            .join(", ")}
                        </Typography>

                        {order?.transportService === "JTEXPRESS" && (
                          <Box sx={{ mt: 3, pt: 2, borderTop: "1px dashed #e0e0e0" }}>
                            <Button
                              variant="contained"
                              onClick={handleClickDownloadTransportOrder}
                              disabled={printing}
                              startIcon={
                                printing ? (
                                  <CircularProgress size={20} color="inherit" />
                                ) : (
                                  <GetApp />
                                )
                              }
                              sx={{
                                textTransform: "none",
                                bgcolor: "#1976d2",
                                "&:hover": {
                                  bgcolor: "#1565c0",
                                },
                              }}
                            >
                              {printing ? "Đang tải..." : "Tải xuống vận đơn"}
                            </Button>
                          </Box>
                        )}
                      </Box>
                    )}
                  </Box>
                </Box>
              </Box>
            </Card>

            {/* Tax Invoice Card */}
            {/* {order?.taxInvoice && ( */}
            <Card
              sx={{
                borderRadius: 1,
                boxShadow: "0 4px 12px rgba(0,0,0,0.05)",
                transition: "box-shadow 0.3s ease-in-out",
                // "&:hover": {
                //   boxShadow: "0 8px 24px rgba(0,0,0,0.12)",
                // },
              }}
            >
              <Box sx={{ p: 2 }}>
                <Box sx={{ display: "flex", alignItems: "flex-start" }}>
                  <Receipt sx={{ color: "#2e7d32", mr: 2, fontSize: 28 }} />
                  <Box sx={{ width: "100%" }}>
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 700,
                        mb: 2,
                        pb: 1,
                        borderBottom: "1px solid #f0f0f0",
                      }}
                    >
                      Xuất hoá đơn GTGT
                    </Typography>

                    <Grid container spacing={1} sx={{ display: "flex", flexDirection: "column" }}>
                      {order?.taxInvoice && (
                        <>
                          <Grid>
                            <Typography sx={{ color: "#757575", fontSize: "0.875rem" }}>
                              {order?.taxInvoice?.taxPayerType === "Individual"
                                ? "Họ tên:"
                                : "Tên công ty:"}
                            </Typography>
                            <Typography sx={{ fontWeight: 500, mt: 0, fontSize: 14 }}>
                              {order?.taxInvoice?.name}
                            </Typography>
                          </Grid>

                          <Grid>
                            <Typography sx={{ color: "#757575", fontSize: "0.875rem" }}>
                              Mã số thuế:
                            </Typography>
                            <Typography sx={{ fontWeight: 500, mt: 0, fontSize: 14 }}>
                              {order?.taxInvoice?.taxCode}
                            </Typography>
                          </Grid>

                          <Grid>
                            <Typography sx={{ color: "#757575", fontSize: "0.875rem" }}>
                              Email:
                            </Typography>
                            <Typography sx={{ fontWeight: 500, mt: 0, fontSize: 14 }}>
                              {order?.taxInvoice?.email}
                            </Typography>
                          </Grid>
                          <Grid>
                            <Typography sx={{ color: "#757575", fontSize: "0.875rem" }}>
                              Địa chỉ:
                            </Typography>
                            <Typography sx={{ fontWeight: 500, mt: 0, fontSize: 14 }}>
                              {order?.taxInvoice?.address}
                            </Typography>
                          </Grid>
                        </>
                      )}
                    </Grid>
                  </Box>
                </Box>
              </Box>
              {!order?.taxInvoice && (
                <>
                  <Typography
                    sx={{
                      textAlign: "center",
                      marginTop: 0,
                      marginBottom: 2,
                      color: "#6C737F",
                      fontSize: "15px",
                    }}
                  >
                    Không có dữ liệu
                  </Typography>
                </>
              )}
            </Card>
            <Card
              sx={{
                mb: 2,
                borderRadius: 1,
                boxShadow: "0 4px 12px rgba(0,0,0,0.05)",
                transition: "box-shadow 0.3s ease-in-out",
                overflow: "visible",
              }}
            >
              <NotesSection
                onSave={handleClickSaveNotes}
                notes={order?.notes}
                statusOrder={order?.statusOrder}
              />
            </Card>
            {/* )} */}
          </RightColumn>
        </Grid>
      </Box>
    </DashboardLayout>
  );
}

const NotesSection = ({ onSave, notes, statusOrder }) => {
  const isDisable = statusOrder == "Failed" || statusOrder == "Success";
  const [currentNotes, setCurrentNotes] = useState("");
  const handleSave = () => {
    onSave(currentNotes);
  };

  useEffect(() => {
    setCurrentNotes(notes);
  }, [notes]);

  return (
    <Card sx={{ p: 2, marginTop: 3 }}>
      <Typography
        gutterBottom
        marginBottom={1}
        variant="h6"
        sx={{
          fontWeight: 700,
          mb: 1,
          display: "flex",
          alignItems: "start",
        }}
      >
        <EventNote sx={{ color: "rgb(36, 18, 168)", mr: 2, fontSize: 28 }} />
        Ghi chú đơn hàng
      </Typography>
      <TextField
        value={currentNotes || ""}
        multiline
        rows={4}
        variant="outlined"
        fullWidth
        onChange={(e) => setCurrentNotes(e.target.value)}
        disabled={isDisable}
      />
      {!isDisable && (
        <Box display="flex" justifyContent="end" marginTop={2}>
          <Button variant="contained" onClick={handleSave}>
            Lưu
          </Button>
        </Box>
      )}
    </Card>
  );
};
