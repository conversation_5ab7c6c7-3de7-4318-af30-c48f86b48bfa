import { Tooltip, Typography, TypographyProps, SxProps, Theme } from "@mui/material";
import Link from "next/link";

interface TruncatedTextProps {
  text: string;
  isLink?: boolean;
  link?: string;
  openInNewTab?: boolean;
  typographyProps?: Partial<Omit<TypographyProps, "children" | "onClick" | "sx">> & {
    sx?: SxProps<Theme>;
  };
}

const TruncatedText = ({
  text,
  isLink = false,
  link = "",
  openInNewTab = true,
  typographyProps = {},
}: TruncatedTextProps) => {
  const handleClick = () => {
    if (isLink && link) {
      if (openInNewTab) {
        window.open(link, "_blank");
      } else {
        window.location.href = link;
      }
    }
  };

  const defaultSx: SxProps<Theme> = {
    // fontSize: 14,
    display: "-webkit-box",
    WebkitLineClamp: 1,
    WebkitBoxOrient: "vertical",
    overflow: "hidden",
    textOverflow: "ellipsis",
    lineHeight: "1.2em",
    maxHeight: "2.4em",
    cursor: isLink ? "pointer" : "default",
    ...(isLink && {
      "&:hover": {
        color: "primary.main",
        textDecoration: "underline",
      },
    }),
  };

  const mergedSx: SxProps<Theme> = {
    ...defaultSx,
    ...(typographyProps.sx || {}),
  };

  const finalTypographyProps: TypographyProps = {
    ...typographyProps,
    onClick: handleClick,
    sx: mergedSx,
  };

  return (
    <Tooltip title={text} placement="top-start" arrow>
      {isLink ? (
        <Link href={link}>
          <Typography {...finalTypographyProps}>{text}</Typography>
        </Link>
      ) : (
        <Typography {...finalTypographyProps}>{text}</Typography>
      )}
    </Tooltip>
  );
};

export default TruncatedText;
