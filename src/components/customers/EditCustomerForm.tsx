import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";
import {
  Box,
  Button,
  Card,
  Divider,
  IconButton,
  InputAdornment,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { FormProvider, useForm } from "react-hook-form";
import { useRouter } from "next/router";
import { useDispatch } from "react-redux";
import CustomerTag from "./CustomerTag";
import { editCustomerInfoSchema } from "@/src/utils/validations/validationSchema";
import { yupResolver } from "@hookform/resolvers/yup";
import ListAddress from "./ListAddress";
import { AppDispatch } from "@/src/store";
import { useStoreId } from "@/src/hooks/use-store-id";
import TitleDialog from "../dialog/TitleDialog";
import { useTranslation } from "react-i18next";
import NavigateNextOutlinedIcon from "@mui/icons-material/NavigateNextOutlined";
import { useUser } from "@/src/api/hooks/user/use-user";
import { useOrder } from "@/src/api/hooks/order/use-order";
import dayjs from "dayjs";
import _ from "lodash";
import { TableOrderFilterType } from "../orders/draft/TableOrder";
import Link from "next/link";
import { formatMoney } from "@/src/utils/format-money";
import { UpdateUserInfoBody } from "@/src/api/types/user.types";
import useSnackbar from "@/src/hooks/use-snackbar";
import { paths } from "@/src/paths";
import {
  getOrderStatusPayLabel,
  OrderProductPayStatusWithBg,
  OrderProductTransportStatusWithBg,
} from "@/src/utils/order/order-helper";
const defaultValues = {
  email: "",
  name: "",
  phone: "",
  tags: [],
};
import * as Yup from "yup";
import FormUserInfo from "./FormUserInfo";
import { tokens } from "@/src/locales/tokens";
import { ContentCopy } from "@mui/icons-material";
import { Eye, EyeOff } from "@untitled-ui/icons-react";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import { usePathname } from "next/navigation";
import EditIcon from "@mui/icons-material/Edit";
import TruncatedText from "../truncated-text/truncated-text";
import { useMembershipLevel } from "@/src/api/hooks/membership-level/use-membership-level";
import { MembershipLevel } from "@/src/api/types/membership-level.types";
import { useAppSelector } from "@/src/redux/hooks";

export const defaultShippingValues = {
  shippingAddressFullname: "",
  shippingAddressPhone: "",
  shippingAddressAddress: "",
  shippingAddressProvince: "",
  shippingAddressWard: "",
  shippingAddressDistrict: "",
};

export default function EditCustomerForm({ customer, onSubmit, onDelete, fetchUser }) {
  const { t } = useTranslation();
  const validationSchema = editCustomerInfoSchema(t);
  const methods = useForm({
    defaultValues,
    resolver: yupResolver(validationSchema),
  });

  const [confirmDialog, setConfirmDialog] = useState<{ open: boolean; customer: any }>({
    open: false,
    customer: null,
  });

  const { permissions } = useAllPermissions();
  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };

  const {
    control,
    handleSubmit,
    register,
    setValue,
    formState: { errors },
  } = methods;
  const dispatch = useDispatch<AppDispatch>();
  const snackbar = useSnackbar();
  const { updateUserInfo } = useUser();
  // Cập nhật form khi có customer (trường hợp edit)
  // useEffect(() => {
  //   if (customer) {
  //     setValue('name', customer?.fullname || '');
  //     setValue('email', customer?.email || '');
  //     setValue('phone', customer?.phoneNumber || '');
  //   }
  // }, [customer, setValue]);

  const onSubmitForm = (data) => {
    onSubmit(data);
    onSubmit(data); // Gọi hàm xử lý submit từ props
  };

  const router = useRouter();
  const handleCancel = () => {
    router.back();
  };

  const handleOpenConfirmDialog = (currentCustomer) => {
    setConfirmDialog({ open: true, customer });
  };

  const handleCloseConfirmDialog = () => {
    setConfirmDialog({ open: false, customer: null });
  };

  const handleConfirmDelete = () => {
    if (confirmDialog.customer) {
      onDelete();
    }
    handleCloseConfirmDialog();
  };

  const handleSubmitTag = async (tags) => {
    const data: UpdateUserInfoBody = {
      userId: customer.userId,
      updateAction: "UpdateTags",
      tags,
    };

    const response = await updateUserInfo(data);
    if (response?.data) {
      snackbar.success("Cập nhật nhãn khách hàng thành công");
    }
  };
  return (
    <>
      <FormProvider {...methods}>
        <Grid container spacing={2}>
          {/* Cột chiếm 8 phần */}
          <Grid size={{ xs: 12, md: 7.5 }}>
            <UserInfo
              customer={customer}
              isGranted={isGranted(paths.customers.list, PERMISSION_TYPE_ENUM.Edit)}
            />
            <Box marginTop={3}>
              <ListOrder customer={customer} />
            </Box>
          </Grid>

          {/* Cột chiếm 4 phần */}
          <Grid size={{ xs: 12, md: 4.5 }}>
            <Card sx={{ marginTop: 0 }}>
              <UserInfo2
                customer={customer}
                fetchUser={fetchUser}
                isGranted={isGranted(paths.customers.list, PERMISSION_TYPE_ENUM.Edit)}
              />
            </Card>
            <Card sx={{ marginTop: 2, p: 2 }}>
              <CustomerTag
                submitTag={handleSubmitTag}
                dataTags={customer ? customer.tags : []}
                isGranted={isGranted(paths.customers.list, PERMISSION_TYPE_ENUM.Edit)}
              />
            </Card>
            <Card sx={{ p: 2, marginTop: 2 }}>
              <UserNotes
                notes={customer?.notes}
                userId={customer?.userId}
                isGranted={isGranted(paths.customers.list, PERMISSION_TYPE_ENUM.Edit)}
              />
            </Card>
          </Grid>
        </Grid>
      </FormProvider>
      <Grid container sx={{ mt: 4, mb: 4 }} spacing={2}></Grid>
      <Divider />
      <Grid container sx={{ mt: 3 }}>
        <Box display="flex" gap={1}>
          <Button variant="outlined" onClick={handleCancel}>
            Hủy bỏ
          </Button>
        </Box>
      </Grid>
    </>
  );
}

const UserInfo = ({ customer, isGranted }) => {
  const [openDialog, setOpenDialog] = useState(false);
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [newPasswordError, setNewPasswordError] = useState("");
  const [confirmPasswordError, setConfirmPasswordError] = useState("");
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const currentShop = useAppSelector((state) => state.shop.currentShop);
  const snackbar = useSnackbar();
  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const { updateUserInfo } = useUser();

  const onClickOpenDialog = () => {
    setOpenDialog(true);
  };

  const validatePassword = (password: string): { isValid: boolean; error: string } => {
    if (!password) {
      return { isValid: false, error: "Vui lòng nhập mật khẩu" };
    }

    if (password.length < 8) {
      return { isValid: false, error: "Mật khẩu phải có ít nhất 8 ký tự" };
    }

    if (!/[A-Z]/.test(password)) {
      return { isValid: false, error: "Mật khẩu phải có ít nhất 1 chữ hoa" };
    }

    if (!/[a-z]/.test(password)) {
      return { isValid: false, error: "Mật khẩu phải có ít nhất 1 chữ thường" };
    }

    if (!/[0-9]/.test(password)) {
      return { isValid: false, error: "Mật khẩu phải có ít nhất 1 chữ số" };
    }

    if (!/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password)) {
      return { isValid: false, error: "Mật khẩu phải có ít nhất 1 ký tự đặc biệt" };
    }

    return { isValid: true, error: "" };
  };

  const validatePasswords = () => {
    const newPasswordValidation = validatePassword(newPassword);
    setNewPasswordError(newPasswordValidation.error);

    if (!confirmPassword) {
      setConfirmPasswordError("Vui lòng xác nhận mật khẩu");
      return false;
    }

    if (newPassword !== confirmPassword) {
      setConfirmPasswordError("Mật khẩu không khớp");
      return false;
    }

    setConfirmPasswordError("");
    return newPasswordValidation.isValid;
  };

  const handleNewPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setNewPassword(value);
    if (value) {
      const validation = validatePassword(value);
      setNewPasswordError(validation.error);
    } else {
      setNewPasswordError("");
    }

    if (confirmPassword && value !== confirmPassword) {
      setConfirmPasswordError("Mật khẩu không khớp");
    } else if (confirmPassword) {
      setConfirmPasswordError("");
    }
  };

  const handleConfirmPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setConfirmPassword(value);

    if (!value) {
      setConfirmPasswordError("");
      return;
    }

    if (newPassword !== value) {
      setConfirmPasswordError("Mật khẩu không khớp");
    } else {
      setConfirmPasswordError("");
    }
  };

  const handleSubmit = async () => {
    if (!validatePasswords()) return;
    const data: UpdateUserInfoBody = {
      userId: customer.userId,
      updateAction: "ChangePassword",
      password: newPassword,
    };

    const response = await updateUserInfo(data);
    if (response?.data) {
      handleCloseDialog();
      snackbar.success("Đổi mật khẩu thành công");
      setNewPassword("");
      setConfirmPassword("");
    }
  };
  return (
    <Card sx={{ p: 2 }}>
      <Box display="flex" gap={2}>
        <Box
          sx={{
            width: 60,
            height: 60,
            overflow: "hidden",
            borderRadius: "50%",
            boxShadow: 3,
            flexShrink: 0,
          }}
        >
          <img
            src={customer?.avatar || currentShop?.shopLogo?.link}
            alt={customer?.fullname}
            style={{
              width: "100%",
              height: "100%",
              objectFit: "cover",
            }}
          />
        </Box>

        <Box sx={{ flex: 1 }}>
          <Box
            display="flex"
            justifyContent={{ md: "space-between", xs: "flex-start" }}
            flexDirection={{ xs: "column", md: "row" }}
          >
            <Box sx={{ width: "30%" }}>
              <Typography fontWeight="bold">
                <TruncatedText
                  typographyProps={{ fontSize: 20, fontWeight: 500 }}
                  text={customer?.fullname}
                />
              </Typography>
              {/* <Typography>Sh11 Lenvender, định công, hoàng mai, hà nội</Typography> */}
            </Box>

            <Box display="flex" gap={{ sx: 0, md: 1 }} flexDirection={{ xs: "column", md: "row" }}>
              {isGranted ? (
                <Typography
                  sx={{
                    cursor: "pointer",
                    color: "text.primary",
                    transition: "color 0.2s",
                    "&:hover": {
                      color: "primary.main",
                      textDecoration: "underline",
                    },
                  }}
                  onClick={onClickOpenDialog}
                >
                  Đặt lại mật khẩu
                </Typography>
              ) : (
                <Tooltip title="Bạn không có quyền">
                  <span>
                    <Typography
                      sx={{
                        cursor: "pointer",
                        opacity: 0.5,
                      }}
                    >
                      Đặt lại mật khẩu
                    </Typography>
                  </span>
                </Tooltip>
              )}
              {/* <Typography sx={{ cursor: "pointer" }} color="error">
                Vô hiệu hóa
              </Typography> */}

              <TitleDialog
                title="Đặt lại mật khẩu"
                open={openDialog}
                handleClose={handleCloseDialog}
                submitBtnTitle="Lưu"
                handleSubmit={handleSubmit}
                showActionDialog={true}
              >
                <Box sx={{ display: "flex", flexDirection: "column", gap: 0.5, mt: 0.5 }}>
                  <Typography sx={{ fontSize: 15, fontWeight: 500, display: "flex" }}>
                    Mật khẩu <Typography sx={{ color: "red", marginLeft: 0.5 }}>*</Typography>
                  </Typography>
                  <TextField
                    value={newPassword}
                    variant="outlined"
                    type={showNewPassword ? "text" : "password"}
                    fullWidth
                    onChange={handleNewPasswordChange}
                    placeholder="Nhập mật khẩu mới"
                    required
                    error={!!newPasswordError}
                    helperText={newPasswordError}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowNewPassword(!showNewPassword)}
                            edge="end"
                            aria-label="toggle password visibility"
                          >
                            {showNewPassword ? <EyeOff /> : <Eye />}
                          </IconButton>
                        </InputAdornment>
                      ),
                      sx: {
                        "& input::-ms-reveal, & input::-ms-clear": {
                          display: "none",
                        },
                        height: "45px",
                      },
                    }}
                  />

                  <Typography
                    sx={{ marginTop: 2.5, display: "flex", fontSize: 15, fontWeight: 500 }}
                  >
                    Nhập lại mật khẩu{" "}
                    <Typography sx={{ color: "red", marginLeft: 0.5 }}>*</Typography>
                  </Typography>
                  <TextField
                    value={confirmPassword}
                    variant="outlined"
                    type={showConfirmPassword ? "text" : "password"}
                    fullWidth
                    onChange={handleConfirmPasswordChange}
                    placeholder="Nhập lại mật khẩu mới"
                    required
                    error={!!confirmPasswordError}
                    helperText={confirmPasswordError}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            edge="end"
                            aria-label="toggle password visibility"
                          >
                            {showConfirmPassword ? <EyeOff /> : <Eye />}
                          </IconButton>
                        </InputAdornment>
                      ),
                      sx: {
                        "& input::-ms-reveal, & input::-ms-clear": {
                          display: "none",
                        },
                        height: "45px",
                      },
                    }}
                  />

                  <Typography variant="caption" sx={{ color: "text.secondary" }}>
                    Mật khẩu phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường, số và ký tự đặc
                    biệt.
                  </Typography>
                </Box>
              </TitleDialog>
            </Box>
          </Box>

          <Box display="flex" justifyContent="space-between">
            <Box sx={{ width: "30%" }}>
              <TruncatedText
                typographyProps={{ fontSize: 14 }}
                text={customer?.membershipLevel && customer?.membershipLevel?.levelName}
              />
            </Box>

            <Box display="flex" sx={{ cursor: "pointer" }} alignItems="center">
              <Typography noWrap>{customer?.point} điểm</Typography>
              <NavigateNextOutlinedIcon />
            </Box>
          </Box>
        </Box>
      </Box>
    </Card>
  );
};

const ListOrder = ({ customer }) => {
  const storeId = useStoreId();

  const { listOrder, loading } = useOrder();
  const router = useRouter();
  const [orders, setOrders] = useState([]);
  const [totalOrders, setTotalOrders] = useState(0);

  const fetchOrderList = async (data) => {
    const skip = 0;
    const limit = 5;
    const response = await listOrder(skip, limit, data);
    if (response && response.data) {
      setOrders(response.data.data || []);
      setTotalOrders(response.data.total || 0);
      // setTotalCount(response.data.total || 0);
    }
  };

  // Wrap fetchOrderList with debounce
  const debouncedFetchOrderList = useCallback(
    _.debounce((data) => {
      fetchOrderList(data);
    }, 400), // Delay 1s
    []
  );
  useEffect(() => {
    if (customer && storeId) {
      const data: TableOrderFilterType = {
        shopId: storeId,
        search: "",
        statusOrder: "All",
        statusTransport: "All",
        statusPay: "All",
        userId: customer.userId,
      };
      debouncedFetchOrderList(data);
    }
  }, [customer, storeId]);
  return (
    <Card sx={{ p: 2 }}>
      <Box>
        <Typography variant="h6">Lịch sử mua hàng</Typography>
      </Box>

      <Divider sx={{ marginTop: 2, marginBottom: 5 }} />
      <Box>
        {orders.map((order) => {
          return (
            <Box key={order.orderId}>
              <Box>
                <Typography color="text.secondary">
                  {dayjs(order.created).format("DD/MM/YYYY HH:mm")}
                </Typography>
              </Box>
              <Box display="flex" justifyContent="space-between">
                <Link href={paths.orders.detail.replace(":id", order.orderId)}>
                  <Typography color="primary">Đơn hàng {order.orderNo}</Typography>
                </Link>
                <Typography>{order.listItems.length} sản phẩm</Typography>
                <Typography>{formatMoney(order.totalAfterTax || 0)}đ</Typography>
              </Box>

              <Box display="flex" gap={1} marginTop={1}>
                <OrderProductTransportStatusWithBg status={order?.statusTransport} />
                <OrderProductPayStatusWithBg status={order?.statusPay} />
              </Box>

              <Divider sx={{ marginTop: 2, marginBottom: 2 }} />
            </Box>
          );
        })}
        {totalOrders === 0 && (
          <Box>
            <Typography color="text.secondary">Chưa có đơn hàng nào</Typography>
          </Box>
        )}
      </Box>
      {totalOrders > 5 && (
        <Box display="flex" justifyContent="end">
          <Link href={`${paths.orders.list}?userId=${customer.userId}`}>
            <Button variant="outlined">Xem tất cả đơn hàng</Button>
          </Link>
        </Box>
      )}
    </Card>
  );
};

const UserNotes = ({ notes, userId, isGranted }) => {
  const [openDialog, setOpenDialog] = useState(false);
  const [currentNotes, setCurrentNotes] = useState("");
  const [finalNotes, setFinalNotes] = useState("");
  const snackbar = useSnackbar();
  const [errorMessage, setErrorMessage] = useState<string>("");
  const { updateUserInfo } = useUser();
  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const onClickOpenDialog = () => {
    setOpenDialog(true);
  };

  const handleSubmit = async () => {
    if (currentNotes.trim().length > 255) {
      setErrorMessage("Ghi chú không được vượt quá 255 ký tự");
      return;
    } else {
      setErrorMessage("");
      const data: UpdateUserInfoBody = {
        userId,
        updateAction: "UpdateNotes",
        notes: currentNotes.trim(),
      };

      const response = await updateUserInfo(data);
      if (response?.data) {
        setFinalNotes(response?.data?.notes);
        handleCloseDialog();
        snackbar.success("Cập nhật ghi chú thành công");
      }
    }
  };

  useEffect(() => {
    setCurrentNotes(notes);
    setFinalNotes(notes);
  }, [notes]);

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center">
        <Typography variant="h6">Ghi chú</Typography>
        <Tooltip title={!isGranted ? "Bạn không có quyền" : ""}>
          <span>
            <Button disabled={!isGranted} onClick={onClickOpenDialog}>
              Sửa
            </Button>
          </span>
        </Tooltip>

        <TitleDialog
          title="Sửa ghi chú"
          open={openDialog}
          handleClose={handleCloseDialog}
          submitBtnTitle="Lưu"
          handleSubmit={handleSubmit}
        >
          <TextField
            value={currentNotes || ""}
            multiline
            rows={6}
            variant="outlined"
            fullWidth
            onChange={(e) => setCurrentNotes(e.target.value)}
          />
          {errorMessage && (
            <Typography sx={{ color: "red", fontSize: 14 }}>{errorMessage}</Typography>
          )}
        </TitleDialog>
      </Box>

      <Typography sx={{ whiteSpace: "pre-line" }} color="text.secondary">
        {finalNotes}
      </Typography>
    </Box>
  );
};

const UserInfo2 = ({ customer, fetchUser, isGranted }) => {
  const [openDialog, setOpenDialog] = useState<boolean>(false);
  const [isOpenModalUpdateUserInfo, setIsOpenModalUpdateUserInfo] = useState<boolean>(false);
  const [currentReferrerCode, setCurrentReferrerCode] = useState<string>("");
  const [finalReferrerCode, setFinalReferrerCode] = useState<string>("");
  const snackbar = useSnackbar();
  const { t } = useTranslation();
  const storeId = useStoreId();
  const { updateUserInfo, updateUser } = useUser();
  const { getMembershipLevel } = useMembershipLevel();
  const [membershipLevels, setMembershipLevels] = React.useState<MembershipLevel[]>([]);
  const defaultUserInfoValues = {
    fullname: "",
    email: "",
    phoneNumber: "",
    birthdate: "",
    gender: "",
    membershipLevelId: "",
  };
  const userInfoSchema = (t) =>
    Yup.object().shape({
      fullname: Yup.string()
        .required("Họ tên là bắt buộc")
        .max(255, "Họ tên không được vượt quá 255 ký tự"),
      phoneNumber: Yup.string()
        .required("Số điện thoại là bắt buộc")
        .matches(/^(\+84|0)[3-9]\d{8}$/, "Số điện thoại không đúng định dạng"),
      // email: Yup.string().required("Email là bắt buộc"),
    });

  const methods = useForm({
    defaultValues: defaultUserInfoValues,
    resolver: yupResolver(userInfoSchema(t)),
  });

  useEffect(() => {
    if (storeId) {
      const fetchMembershipLevel = async () => {
        const res = await getMembershipLevel(0, 99, storeId);
        setMembershipLevels(res?.data?.data || []);
      };
      fetchMembershipLevel();
    }
  }, [storeId]);

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const onClickOpenDialog = () => {
    setOpenDialog(true);
  };

  const handleSubmit = async () => {
    const data: UpdateUserInfoBody = {
      userId: customer.userId,
      updateAction: "UpdateReferrerCode",
      referrerCode: currentReferrerCode,
    };

    const response = await updateUserInfo(data);
    if (response?.data) {
      setFinalReferrerCode(currentReferrerCode);
      handleCloseDialog();
      snackbar.success("Cập nhật người giới thiệu thành công");
    }
  };

  useEffect(() => {
    if (customer) {
      setCurrentReferrerCode(customer.referrerCode);
      setFinalReferrerCode(customer.referrerCode);
    }
  }, [customer]);

  const handleUpdateUserInfo = async (data) => {
    const userData = {
      shopId: storeId,
      userId: customer.userId,
      birthdate: data.birthdate ? dayjs(data.birthdate).format("YYYY-MM-DDTHH:mm:ss.SSS") : null,
      email: data.email,
      fullname: data.fullname,
      gender: data.gender,
      phoneNumber: data.phoneNumber,
      membershipLevelId: data.membershipLevelId || null,
    };
    const response = await updateUser(userData);
    if (response && response.data) {
      snackbar.success(t(tokens.settings.updateSuccess));
      fetchUser();
    }
  };
  return (
    <Box>
      <Box sx={{ p: 2 }}>
        <Box sx={{ display: "flex", justifyContent: "space-between" }}>
          <Typography variant="h6">Thông tin tài khoản</Typography>
          <Tooltip title={!isGranted ? "Bạn không có quyền" : ""}>
            <span>
              <Button
                variant="text"
                sx={{ padding: 0 }}
                disabled={!isGranted}
                onClick={() => setIsOpenModalUpdateUserInfo(true)}
              >
                Quản lý
              </Button>
            </span>
          </Tooltip>
        </Box>
        <Box marginTop={1}>
          <Box
            display="flex"
            gap={1}
            justifyContent={{ md: "space-between", xs: "flex-start" }}
            flexDirection={{ md: "row", xs: "column" }}
          >
            <Box sx={{ width: "100%" }}>
              <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                <Typography sx={{ width: "150px", flexShrink: 0 }}>SĐT:</Typography>
                <Typography>{customer?.phoneNumber}</Typography>
              </Box>

              <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                <Typography sx={{ width: "150px", flexShrink: 0 }}>Email:</Typography>
                <Typography>{customer?.email}</Typography>
              </Box>

              <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                <Typography sx={{ width: "150px", flexShrink: 0 }}>Mã giới thiệu:</Typography>
                <Typography>
                  {customer?.referralCode}
                  <Tooltip title="Sao chép mã">
                    <IconButton
                      size="small"
                      onClick={() => {
                        navigator.clipboard.writeText(customer?.referralCode || "");
                        snackbar.success("Copy thành công mã giới thiệu");
                      }}
                      sx={{ ml: 1 }}
                    >
                      <ContentCopy fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Typography>
              </Box>

              <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                <Typography sx={{ cursor: "pointer", width: "150px", flexShrink: 0 }}>
                  Mã người giới thiệu:
                </Typography>
                {finalReferrerCode && (
                  <Typography>
                    {finalReferrerCode}
                    <Tooltip title="Sao chép mã">
                      <IconButton
                        size="small"
                        onClick={() => {
                          navigator.clipboard.writeText(finalReferrerCode || "");
                          snackbar.success("Copy thành công mã người giới thiệu");
                        }}
                        sx={{ ml: 1 }}
                      >
                        <ContentCopy fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Typography>
                )}
                {isGranted ? (
                  <IconButton
                    aria-label="Sửa"
                    onClick={onClickOpenDialog}
                    sx={{ color: "blue" }}
                    size="small"
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                ) : (
                  <Tooltip title="Bạn không có quyền sửa">
                    <span>
                      <IconButton
                        aria-label="Sửa"
                        sx={{ color: "blue" }}
                        disabled={true}
                        size="small"
                      >
                        <EditIcon fontSize="small" />
                      </IconButton>
                    </span>
                  </Tooltip>
                )}
                <TitleDialog
                  title="Sửa mã người giới thiệu"
                  open={openDialog}
                  handleClose={handleCloseDialog}
                  submitBtnTitle="Lưu"
                  handleSubmit={handleSubmit}
                >
                  <TextField
                    value={currentReferrerCode || ""}
                    variant="outlined"
                    fullWidth
                    onChange={(e) => setCurrentReferrerCode(e.target.value)}
                  />
                </TitleDialog>
                <Typography></Typography>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
      <Divider />

      {customer && (
        // <Grid size={{ xs: 12, md: 4 }}>
        <ListAddress customer={customer} isGranted={isGranted} />
        // </Grid>
      )}

      <FormUserInfo
        open={isOpenModalUpdateUserInfo}
        setOpen={setIsOpenModalUpdateUserInfo}
        userInfo={customer}
        membershipLevels={membershipLevels}
        fetchUser={fetchUser}
      />
    </Box>
  );
};
