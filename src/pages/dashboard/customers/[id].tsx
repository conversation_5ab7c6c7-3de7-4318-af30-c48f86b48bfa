import React, { useEffect, useState } from "react";
import DashboardLayout from "@/src/layouts/dashboard";
import { useRouter } from "next/router";
import PageTitleWithBackBtn from "@/src/layouts/dashboard/page-title-with-back-btn/page-title";
import { Box } from "@mui/system";
import EditCustomerForm from "@/src/components/customers/EditCustomerForm";
import { useUser } from "@/src/api/hooks/user/use-user";
import { getProvinces } from "@/src/slices/addressSlice";
import { useDispatch } from "react-redux";
import { fetchTags } from "@/src/slices/listTagSlice";
import { AppDispatch } from "@/src/store";
import { useSnackbar } from "@/src/hooks/use-snackbar";
import { tokens } from "@/src/locales/tokens";
import { useTranslation } from "react-i18next";
import { paths } from "@/src/paths";
import { Padding } from "@/src/styles/CommonStyle";

export default function CustomerDetail() {
  const { t } = useTranslation();
  const { detailUser, loading, error, updateUser, deleteUsers } = useUser();
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();
  const { id } = router.query;
  const [user, setUser] = useState(null);
  const snackbar = useSnackbar();
  const fetchUser = async () => {
    try {
      const response = await detailUser(id);
      if (response && response.data) {
        const { data } = response;
        setUser(data);
      }
    } catch (error) {
    } finally {
    }
  };
  useEffect(() => {
    if (!id) return; // Đảm bảo `id` đã sẵn sàng

    fetchUser();
    dispatch(getProvinces());
  }, [id]);

  const handleEditUser = async (data) => {
    //Gọi API để chỉnh sửa người dùng ở đây
    const userData = {
      userId: user.userId,
      fullName: data.name,
      email: data.email,
      phoneNumber: data.phone,
      tags: data.tags,
      membershipLevelId: data.membershipLevelId,
    };

    const response = await updateUser(userData);
    if (response && response.data) {
      snackbar.success(t(tokens.settings.updateSuccess));
      fetchUser();
      dispatch(fetchTags({ bodyData: { search: "", shopId: user.shopId } }));
    }
  };

  const handleDeleteUser = async () => {
    const userIds = [user.userId];
    const response = await deleteUsers(userIds);
    if (response && response.data) {
      await router.push(paths.customers.list);
      snackbar.success(t("user.deleteSuccess"));
    }
  };

  return (
    <DashboardLayout>
      <Box sx={{ p: Padding }}>
        <PageTitleWithBackBtn
          title="Chi tiết khách hàng"
          sx={{ marginBottom: 2 }}
          backPath={paths.customers.list}
        />
        <EditCustomerForm
          customer={user}
          onSubmit={handleEditUser}
          onDelete={handleDeleteUser}
          fetchUser={fetchUser}
        />
      </Box>
    </DashboardLayout>
  );
}
