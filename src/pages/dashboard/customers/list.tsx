import { use<PERSON><PERSON>back, useEffect, useState } from "react";
import DashboardLayout from "../../../layouts/dashboard";
import {
  Box,
  Button,
  Card,
  InputAdornment,
  Tab,
  Tabs,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";

import FileUploadOutlinedIcon from "@mui/icons-material/FileUploadOutlined";
import { useRouter } from "next/router";
import { paths } from "@/src/paths";
import TableCustomer from "../../../components/customers/TableCustomer";
import ActionButton from "../../../components/common/ActionButton";
import TitleTypography from "@/src/components/title-typography/title-typography";
import { Download, Search, VerticalAlignBottom } from "@mui/icons-material";
import type { ButtonProps } from "@mui/material";

// import ModalImportCustomer from "../../zalo-automation/campaign/component/modal-import";
import ModalImportExcelCustomer from "@/src/components/components/modal-import-excel-customer";
import { useUser } from "@/src/api/hooks/user/use-user";
import { useStoreId } from "@/src/hooks/use-store-id";
import useSnackbar from "@/src/hooks/use-snackbar";
import _ from "lodash";
import { Padding } from "@/src/styles/CommonStyle";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import { usePathname } from "next/navigation";
import { useDebounce } from "@/src/hooks/use-debounce";
import { width } from "@mui/system";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";

interface ParamGetCustomer {
  FromDate: string;
  ToDate: string;
}
export default function CustomerList() {
  const { exportListUser, listUser, loading } = useUser();
  const storeId = useStoreId();
  const pathname = usePathname();
  const [tabIndex, setTabIndex] = useState(0);
  const [searchText, setSearchText] = useState("");
  const snackbar = useSnackbar();
  const [users, setUsers] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [isOpenModalImport, setIsOpenModalImport] = useState<boolean>(false);
  const router = useRouter();
  const { permissions } = useAllPermissions();
  const debouncedSearchValue = useDebounce(searchText, 500);
  const [chartDateRange, setChartDateRange] = useState<any>([null, null]);
  const [filterData, setFilterData] = useState<ParamGetCustomer>(null);
  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };
  const handleTabChange = (event, newValue) => {
    setTabIndex(newValue);
  };
  const handleAddNew = () => {
    router.push(paths.customers.new);
  };

  const fetchUserList = async (currentPage, pageSize, searchQuery, shopId, filterData) => {
    const skip = searchQuery ? 0 : currentPage * pageSize;
    const limit = pageSize;

    const response = await listUser(`?skip=${skip}&limit=${limit}`, {
      search: searchQuery,
      shopId: shopId,
      FromDate: filterData?.FromDate,
      ToDate: filterData?.ToDate,
    });

    if (response && response.data) {
      setUsers(response.data.data || []);
      setTotalCount(response.data.total || 0);
    }
  };

  const debouncedFetchUserList = useCallback(
    _.debounce((currentPage, pageSize, searchQuery, shopId, filterData) => {
      fetchUserList(currentPage, pageSize, searchQuery, shopId, filterData);
    }, 400),
    []
  );

  useEffect(() => {
    debouncedFetchUserList(page, rowsPerPage, debouncedSearchValue, storeId, filterData);
    return () => {
      debouncedFetchUserList.cancel();
    };
  }, [page, rowsPerPage, debouncedSearchValue, storeId, debouncedFetchUserList, filterData]);

  const handleExportListUser = async () => {
    if (!Array.isArray(users) || users.length === 0) {
      return snackbar.error("Chưa có dữ liệu để xuất file");
    }
    const data: {
      Search?: string;
      TagName?: string;
      ShopId?: string;
      AffiliationStatus?: string;
    } = {
      ShopId: storeId,
      Search: debouncedSearchValue,
    };
    const response = await exportListUser(data);
    const reader = new FileReader();

    reader.onload = async function () {
      try {
        if (typeof reader.result === "string") {
          const jsonResponse = JSON.parse(reader.result);

          if (jsonResponse.data && jsonResponse.data.link) {
            const fileName = jsonResponse.data.link.split("/").pop();

            const fileResponse = await fetch(jsonResponse.data.link);
            const fileBlob = await fileResponse.blob();

            const url = window.URL.createObjectURL(fileBlob);
            const link = document.createElement("a");
            link.href = url;
            link.setAttribute("download", fileName);
            document.body.appendChild(link);
            link.click();

            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
          } else {
            console.error("Không tìm thấy link file trong response JSON");
          }
        } else {
          console.error("reader.result không phải là string");
        }
      } catch (error) {
        console.error("Lỗi khi xử lý JSON:", error);
      }
    };

    reader.readAsText(response.data);
    snackbar.success("Tải file thành công");
  };

  const handleChartStartDateChange = (newValue) => {
    setPage(0);
    if (newValue.toString() !== "Invalid Date") {
      const endDate = chartDateRange[1];
      const startD = newValue.startOf("day").format("YYYY-MM-DD HH:mm:ss");
      let updatedRange;

      if (!endDate || newValue.isAfter(endDate)) {
        updatedRange = [newValue, newValue];
        setFilterData((prev) => ({
          ...prev,
          FromDate: startD,
          ToDate: newValue.endOf("day").format("YYYY-MM-DD HH:mm:ss"),
        }));
      } else {
        const endD = endDate.endOf("day").format("YYYY-MM-DD HH:mm:ss");
        updatedRange = [newValue, endDate];
        setFilterData((prev) => ({ ...prev, FromDate: startD, ToDate: endD }));
      }
      setChartDateRange(updatedRange);
    }
  };

  const handleChartEndDateChange = (newValue) => {
    setPage(0);
    const validDayjs = dayjs(newValue);

    if (validDayjs.isValid() && validDayjs.year() >= 1000) {
      const startDate = chartDateRange[0];
      const endD = newValue.endOf("day").format("YYYY-MM-DD HH:mm:ss");
      let updatedRange;

      if (!startDate || newValue.isBefore(startDate, "day")) {
        updatedRange = [newValue, newValue];
        setFilterData((prev) => ({
          ...prev,
          FromDate: newValue.startOf("day").format("YYYY-MM-DD HH:mm:ss"),
          ToDate: endD,
        }));
      } else {
        const startD = startDate.startOf("day").format("YYYY-MM-DD HH:mm:ss");
        updatedRange = [startDate, newValue];
        setFilterData((prev) => ({ ...prev, FromDate: startD, ToDate: endD }));
      }
      setChartDateRange(updatedRange);
    }
  };
  return (
    <DashboardLayout>
      <Box sx={{ p: Padding, paddingTop: "20px" }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            borderBottom: "1px solid #bdbdbd",
            paddingBottom: "8px",
          }}
          flexDirection={{
            xs: "column",
            sm: "row",
          }}
        >
          <TitleTypography
            sx={{
              textTransform: "none",
              color: " #000 !important",
              fontSize: "20px !important",
              fontWeight: "700",
              lineHeight: "20px",
              mb: { xs: 1, sm: 0 },
            }}
          >
            Danh sách khách hàng
          </TitleTypography>
        </Box>
        <Card sx={{ p: 2 }}>
          <Tabs
            value={tabIndex}
            onChange={handleTabChange}
            sx={{
              mb: 2,
              "& .MuiTabs-indicator": {
                background: "#2654FE",
              },
            }}
          >
            <Tab sx={{ color: "#2654FE !important" }} label="Tất cả" />
          </Tabs>
          <Box
            sx={{
              mb: 3,
              display: "flex",
              flexDirection: { xs: "column", sm: "row" },
              gap: 1,
              alignItems: "end",
              justifyContent: "space-between",
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <Box>
                <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="vi">
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <DatePicker
                      value={chartDateRange[0]}
                      format="DD/MM/YYYY"
                      onChange={handleChartStartDateChange}
                      slotProps={{
                        textField: {
                          size: "small",
                          sx: {
                            width: { xs: "100%", sm: 200 },
                            flex: { xs: 1, sm: "none" },
                            "& .MuiOutlinedInput-root": {
                              height: 36,
                              borderRadius: "8px",
                              fontSize: 14,
                            },
                          },
                        },
                      }}
                    />
                    <Typography sx={{ mx: 1 }}> - </Typography>
                    <DatePicker
                      value={chartDateRange[1]}
                      format="DD/MM/YYYY"
                      onChange={handleChartEndDateChange}
                      shouldDisableDate={(date) => date.isBefore(chartDateRange[0], "day")}
                      slotProps={{
                        textField: {
                          size: "small",
                          sx: {
                            width: { xs: "100%", sm: 200 },
                            flex: { xs: 1, sm: "none" },
                            "& .MuiOutlinedInput-root": {
                              height: 36,
                              borderRadius: "8px",
                              fontSize: 14,
                            },
                          },
                        },
                      }}
                    />
                  </Box>
                </LocalizationProvider>
              </Box>
              <Box>
                <TextField
                  placeholder="Tìm kiếm khách hàng"
                  variant="outlined"
                  size="small"
                  sx={{ width: { xs: "100%", sm: "300px" } }}
                  onChange={(e) => setSearchText(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Search />
                      </InputAdornment>
                    ),
                  }}
                />
              </Box>
            </Box>
            <Box
              sx={{
                display: "flex",
                flexDirection: "row",
                gap: { xs: 1, sm: 1 },
                flexWrap: { xs: "wrap", sm: "nowrap" },
                width: { xs: "100%", sm: "auto" },
              }}
            >
              <Box sx={{ display: "flex", gap: 1, justifyContent: "space-between", width: "100%" }}>
                <ActionButton
                  permission={PERMISSION_TYPE_ENUM.Import}
                  tooltip="Bạn không có quyền import"
                  startIcon={<FileUploadOutlinedIcon />}
                  onClick={() => setIsOpenModalImport(true)}
                  isGranted={isGranted}
                  pathname={pathname}
                  size="medium"
                  color="primary"
                  customSx={{ width: "50%" }}
                >
                  Import
                </ActionButton>
                <ActionButton
                  permission={PERMISSION_TYPE_ENUM.Export}
                  tooltip="Bạn không có quyền export"
                  startIcon={<VerticalAlignBottom />}
                  onClick={handleExportListUser}
                  isGranted={isGranted}
                  pathname={pathname}
                  size="medium"
                  color="primary"
                  customSx={{ width: "50%" }}
                >
                  Export
                </ActionButton>
              </Box>
              <ActionButton
                permission={PERMISSION_TYPE_ENUM.Add}
                tooltip="Bạn không có quyền thêm khách hàng"
                variant="contained"
                onClick={handleAddNew}
                isGranted={isGranted}
                pathname={pathname}
                size="medium"
                color="primary"
                customSx={{ width: "calc(50% - 6px)" }}
              >
                Thêm khách hàng
              </ActionButton>
            </Box>
          </Box>

          {tabIndex === 0 && (
            <TableCustomer
              users={users}
              page={page}
              setPage={setPage}
              rowsPerPage={rowsPerPage}
              setRowsPerPage={setRowsPerPage}
              totalCount={totalCount}
              searchText={searchText}
              isGranted={isGranted}
              fetchUserList={fetchUserList}
            />
          )}
        </Card>
      </Box>
      <ModalImportExcelCustomer open={isOpenModalImport} setOpen={setIsOpenModalImport} />
    </DashboardLayout>
  );
}
