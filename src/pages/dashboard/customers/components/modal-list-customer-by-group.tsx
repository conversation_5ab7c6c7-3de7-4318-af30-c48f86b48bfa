import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";
import {
  Search,
  NavigateBefore as NavigateBeforeIcon,
  NavigateNext as NavigateNextIcon,
  FileUpload,
  Download,
} from "@mui/icons-material";
import {
  Box,
  Button,
  CircularProgress,
  IconButton,
  InputAdornment,
  MenuItem,
  Modal,
  Paper,
  Select,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Tooltip,
  Typography,
  useTheme,
  useMediaQuery,
} from "@mui/material";
import * as Yup from "yup";
import { useFormik } from "formik";
import ModalImportMoreCustomerExcel from "./modal-import-more-customer-excel";
import { useUserGroup } from "@/src/api/hooks/user-group/use-user-group";
import type { UserOfUserGroupDto } from "../customer-classification";
import { useStoreId } from "@/src/hooks/use-store-id";
import useSnackbar from "@/src/hooks/use-snackbar";
import type {
  BodyExportExcelListUser,
  SearchUserOfUserGroupDto,
} from "@/src/api/services/user-group/user-group.service";
import { debounce } from "lodash";
import { useRouter } from "next/router";
import { paths } from "@/src/paths";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import { usePathname } from "next/navigation";
import { useDebounce } from "@/src/hooks/use-debounce";
import ActionButton from "@/src/components/common/ActionButton";
import CloseIcon from "@mui/icons-material/Close";
import TruncatedText from "@/src/components/truncated-text/truncated-text";

interface FormValues {
  compaignName: string;
  activationCondition: string;
  description: string;
  typeCompaign: string;
  weeklySchedule: string;
  monthlySchedule: string;
  typeTemplateMessage: string;
  message: string;
  runTime: string;
  startDate: string;
  endDate: string;
  receiver: string;
  typeGroupMember: string;
  submit: string | null;
}

const getAutoStyle = (type) => {
  if (type !== "Manual") {
    return {
      title: "Tự động",
      color: "#fff8e1",
      textColor: "#f9a825",
    };
  } else {
    return {
      title: "Thủ công",
      color: "#f5f5f5",
      textColor: "#616161",
    };
  }
};

const ModalListCustomerByGroup = ({ open, setOpen, selectedUserGroup }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));
  const isSmallMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const { getListUserByGroupId, exportExcelListUserByGroupId } = useUserGroup();
  const [isOpenModalUploadFile, setIsOpenModalUploadFile] = React.useState(false);
  const [isLoadingExportFile, setIsLoadingExportFile] = useState<boolean>(false);
  const [templateMessage, setTemplateMessage] = React.useState("");
  const shopId = useStoreId();
  const [rows, setRows] = useState<UserOfUserGroupDto[]>([]);
  const [selected, setSelected] = useState<number[]>([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState<number>();
  const [searchText, setSearchText] = useState<string>("");
  const snackbar = useSnackbar();
  const [isOpenModalImportMore, setIsOpenModalImportMore] = useState<boolean>(false);
  const validationSchema = Yup.object({
    compaignName: Yup.string().required("Tên chiến dịch không được để trống"),
  });
  const router = useRouter();
  const pathname = usePathname();
  const debouncedSearchValue = useDebounce(searchText, 500);

  const { permissions } = useAllPermissions();
  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };

  // Responsive search field props
  const searchFieldProps = {
    placeholder: "Tìm kiếm",
    variant: "outlined" as const,
    size: "small" as const,
    sx: {
      width: { xs: "100%", sm: "100%", md: 240 },
      mb: { xs: 2, sm: 2, md: 0 },
      "& .MuiOutlinedInput-root": {
        borderRadius: 30,
        backgroundColor: "#fff",
      },
    },
    InputProps: {
      startAdornment: (
        <InputAdornment position="start">
          <Search fontSize="small" />
        </InputAdornment>
      ),
    },
  };

  // Responsive table container props
  const tableContainerProps = {
    sx: {
      overflowX: "auto",
      maxWidth: "100%",
      maxHeight: { xs: "60vh", sm: "70vh", md: "none" },
    },
  };

  // Responsive pagination box props
  const paginationBoxProps = {
    sx: {
      display: "flex",
      justifyContent: { xs: "center", sm: "flex-end" },
      alignItems: "center",
      px: { xs: 1, sm: 2 },
      py: 1.5,
      flexDirection: { xs: "column", sm: "row" },
      gap: { xs: 2, sm: 1 },
    },
  };

  const fetchUserOfUserGroup = async () => {
    if (!selectedUserGroup?.groupId || !shopId) return;

    try {
      const data: SearchUserOfUserGroupDto = {
        groupId: selectedUserGroup?.groupId,
        shopId: shopId,
        paging: {
          pageIndex: page,
          pageSize: rowsPerPage,
          search: debouncedSearchValue.trim(),
        },
      };

      const res = await getListUserByGroupId(data);

      if (res?.status === 200) {
        setRows(res?.data?.result || []);
        setTotalCount(res?.data?.total || 0);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    }
  };

  const debouncedFetch = useCallback(
    debounce(() => {
      fetchUserOfUserGroup();
    }, 500),
    [shopId, selectedUserGroup, page, rowsPerPage, debouncedSearchValue, isOpenModalImportMore]
  );

  useEffect(() => {
    if (page !== 0) {
      setPage(0);
    } else {
      debouncedFetch();
    }

    return () => {
      debouncedFetch.cancel();
    };
  }, [debouncedSearchValue]);

  useEffect(() => {
    fetchUserOfUserGroup();
  }, [shopId, selectedUserGroup, page, rowsPerPage, isOpenModalImportMore]);

  const handleChangePage = (event, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(Number.parseInt(event.target.value, 10));
    setPage(0);
  };

  const formik = useFormik<FormValues>({
    initialValues: {
      compaignName: "",
      activationCondition: "",
      description: "",
      typeCompaign: "",
      weeklySchedule: "",
      typeTemplateMessage: "ZNS",
      monthlySchedule: "",
      message: "",
      runTime: "",
      startDate: "",
      endDate: "",
      receiver: "",
      typeGroupMember: "",
      submit: null,
    },
    validationSchema,
    validateOnBlur: false,
    validateOnChange: false,
    onSubmit: async (values, helpers) => {
      try {
      } catch (err) {}
    },
  });

  const isSelected = (id: number) => selected.indexOf(id) !== -1;

  const [conditions, setConditions] = useState([
    {
      field: "",
      operator: "",
      value: "",
      valueEnd: "",
      logicOperator: "and",
    },
  ]);

  const [searchResults, setSearchResults] = useState(null);

  const handleValueChange = (index, value) => {
    const updatedConditions = [...conditions];
    updatedConditions[index].value = value;
    setConditions(updatedConditions);
  };

  const handleValueEndChange = (index, value) => {
    const updatedConditions = [...conditions];
    updatedConditions[index].valueEnd = value;
    setConditions(updatedConditions);
  };

  const addCondition = () => {
    setConditions([
      ...conditions,
      {
        field: "",
        operator: "",
        value: "",
        valueEnd: "",
        logicOperator: "and",
      },
    ]);
  };

  const removeCondition = (index) => {
    if (conditions.length > 1) {
      const updatedConditions = [...conditions];
      updatedConditions.splice(index, 1);
      setConditions(updatedConditions);
    }
  };

  const handleSearch = () => {
    const query = {
      conditions: conditions.map((condition, index) => ({
        field: condition.field,
        operator: condition.operator,
        value: condition.value,
        valueEnd: condition.valueEnd,
        logicOperator: index < conditions.length - 1 ? condition.logicOperator : null,
      })),
    };

    setSearchResults(`Đã thực hiện tìm kiếm với ${conditions.length} điều kiện`);
  };

  const exportToExcel = async () => {
    try {
      setIsLoadingExportFile(true);
      const data: BodyExportExcelListUser = {
        shopId: shopId,
        groupId: selectedUserGroup.groupId,
        paging: {
          pageSize: totalCount,
          pageIndex: page,
          name: "Created",
          sort: "asc",
          nameType: "Created",
          sortType: "asc",
          search: searchText,
        },
      };

      const response = await exportExcelListUserByGroupId(data);

      if (response?.data?.data?.link) {
        const link = document.createElement("a");
        link.href = response.data.data.link;
        link.setAttribute("download", "FileMauNguoiDung.xlsx");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        console.error("Không tìm thấy link để tải file.");
      }
    } catch (error) {
      console.error("Lỗi khi tải template:", error);
      snackbar.error("Tải file mẫu thất bại");
    } finally {
      setIsLoadingExportFile(false);
      snackbar.success("Tải file mẫu thành công");
    }
  };

  return (
    <>
      <Modal
        open={open}
        onClose={() => setOpen(false)}
        sx={{
          overflowY: "scroll",
          display: "flex",
          alignItems: { xs: "flex-start", md: "center" },
          justifyContent: "center",
          pt: { xs: 2, md: 0 },
          pb: { xs: 2, md: 0 },
        }}
      >
        <Box
          sx={{
            position: "relative",
            width: { xs: "95%", sm: "90%", md: "85%", lg: "80%" },
            maxWidth: "1200px",
            bgcolor: "background.paper",
            borderRadius: "8px",
            boxShadow: 24,
            p: { xs: 2, sm: 3 },
            my: { xs: 2, md: 0 },
            maxHeight: { xs: "95vh", md: "90vh" },
            overflow: "auto",
          }}
        >
          {/* Nút đóng X cho mobile */}
          <IconButton
            onClick={() => setOpen(false)}
            sx={{
              position: "absolute",
              top: 8,
              right: 8,
              zIndex: 10,
              display: { xs: "block", sm: "none" },
            }}
          >
            <CloseIcon />
          </IconButton>

          <Box
            sx={{
              padding: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #ddd",
              borderRadius: 2,
            }}
          >
            <Box>
              {/* Header Section - Responsive */}
              <Box
                sx={{
                  display: "flex",
                  flexDirection: { xs: "column", sm: "column", md: "row" },
                  justifyContent: "space-between",
                  alignItems: { xs: "stretch", md: "center" },
                  marginBottom: 2,
                  gap: { xs: 2, sm: 2, md: 0 },
                }}
              >
                <Typography
                  variant="h6"
                  sx={{
                    fontSize: { xs: "1.1rem", sm: "1.25rem" },
                    mb: { xs: 1, sm: 1, md: 0 },
                  }}
                >
                  Danh sách khách hàng
                </Typography>

                {/* Search and Action Buttons */}
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: { xs: "column", sm: "row" },
                    alignItems: { xs: "stretch", sm: "center" },
                    gap: { xs: 1, sm: 2 },
                    width: { xs: "100%", md: "auto" },
                  }}
                >
                  <TextField
                    {...searchFieldProps}
                    onChange={(e) => {
                      setSearchText(e.target.value);
                    }}
                  />

                  {/* Action Buttons Container */}
                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: { xs: "row", sm: "row" },
                      gap: 1,
                      width: { xs: "100%", sm: "auto" },
                    }}
                  >
                    <Box sx={{ width: { xs: "50%", sm: "auto" } }}>
                      <ActionButton
                        permission={PERMISSION_TYPE_ENUM.Export}
                        tooltip="Bạn không có quyền export"
                        isGranted={isGranted}
                        pathname={pathname}
                        onClick={exportToExcel}
                        variant="outlined"
                        startIcon={
                          isLoadingExportFile ? (
                            <CircularProgress size={16} color="inherit" />
                          ) : (
                            <Download />
                          )
                        }
                        loading={isLoadingExportFile}
                        fullWidth
                        sx={{
                          height: 42,
                          fontSize: { xs: "0.875rem", sm: "0.875rem" },
                          width: "100%",
                        }}
                      >
                        Export
                      </ActionButton>
                    </Box>
                    <Box sx={{ width: { xs: "50%", sm: "auto" } }}>
                      <ActionButton
                        permission={PERMISSION_TYPE_ENUM.Import}
                        tooltip="Bạn không có quyền import"
                        isGranted={isGranted}
                        pathname={pathname}
                        onClick={() => setIsOpenModalImportMore(true)}
                        variant="contained"
                        startIcon={
                          <FileUpload sx={{ fontSize: 18, fontWeight: 500, marginRight: 1 }} />
                        }
                        fullWidth
                        sx={{
                          height: 42,
                          fontSize: { xs: "0.875rem", sm: "0.875rem" },
                          width: "100%",
                        }}
                      >
                        Import
                      </ActionButton>
                    </Box>
                  </Box>
                </Box>
              </Box>

              {/* Table Section */}
              <Paper elevation={3} sx={{ borderRadius: 2 }}>
                <TableContainer {...tableContainerProps}>
                  <Table stickyHeader>
                    <TableHead>
                      <TableRow>
                        <TableCell
                          sx={{
                            width: { xs: "40px", sm: "50px" },
                            fontSize: { xs: "0.75rem", sm: "0.875rem" },
                            padding: { xs: "8px 4px", sm: "16px" },
                          }}
                        >
                          STT
                        </TableCell>
                        <TableCell
                          sx={{
                            fontSize: { xs: "0.75rem", sm: "0.875rem" },
                            padding: { xs: "8px 4px", sm: "16px" },
                            minWidth: { xs: "120px", sm: "150px" },
                          }}
                        >
                          Tên khách hàng
                        </TableCell>
                        <TableCell
                          sx={{
                            fontSize: { xs: "0.75rem", sm: "0.875rem" },
                            padding: { xs: "8px 4px", sm: "16px" },
                            minWidth: { xs: "100px", sm: "120px" },
                          }}
                        >
                          Số điện thoại
                        </TableCell>
                        <TableCell
                          sx={{
                            fontSize: { xs: "0.75rem", sm: "0.875rem" },
                            padding: { xs: "8px 4px", sm: "16px" },
                            minWidth: { xs: "120px", sm: "150px" },
                            display: { xs: "none", sm: "table-cell" },
                          }}
                        >
                          Email
                        </TableCell>
                        <TableCell
                          sx={{
                            fontSize: { xs: "0.75rem", sm: "0.875rem" },
                            padding: { xs: "8px 4px", sm: "16px" },
                            minWidth: { xs: "80px", sm: "100px" },
                          }}
                        >
                          Loại nhóm
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {Array.isArray(rows) && rows.length > 0 ? (
                        rows.map((row, index) => {
                          return (
                            <TableRow hover role="checkbox" tabIndex={-1} key={row.userId}>
                              <TableCell
                                sx={{
                                  fontSize: { xs: "0.75rem", sm: "0.875rem" },
                                  padding: { xs: "8px 4px", sm: "16px" },
                                }}
                              >
                                {index + 1}
                              </TableCell>
                              <TableCell
                                onClick={() =>
                                  router.push(
                                    paths.customers.detail.replace(":id", `${row.userId}`)
                                  )
                                }
                                component="th"
                                scope="row"
                              >
                                <TruncatedText
                                  text={row.fullname}
                                  isLink={true}
                                  typographyProps={{ width: 150, fontSize: 15 }}
                                />
                              </TableCell>
                              <TableCell
                                sx={{
                                  fontSize: { xs: "0.75rem", sm: "0.875rem" },
                                  padding: { xs: "8px 4px", sm: "16px" },
                                }}
                              >
                                {row.phoneNumber}
                              </TableCell>
                              <TableCell
                                sx={{
                                  fontSize: { xs: "0.75rem", sm: "0.875rem" },
                                  padding: { xs: "8px 4px", sm: "16px" },
                                  display: { xs: "none", sm: "table-cell" },
                                  wordBreak: "break-word",
                                }}
                              >
                                {row.email}
                              </TableCell>
                              <TableCell
                                sx={{
                                  fontSize: { xs: "0.75rem", sm: "0.875rem" },
                                  padding: { xs: "8px 4px", sm: "16px" },
                                }}
                              >
                                {(() => {
                                  const { title, color, textColor } = getAutoStyle(row.source);
                                  return (
                                    <Typography
                                      sx={{
                                        textAlign: "center",
                                        padding: { xs: "2px 4px", sm: "4px 8px" },
                                        borderRadius: 3,
                                        width: { xs: "100%", sm: "80%" },
                                        backgroundColor: color,
                                        color: textColor,
                                        fontWeight: 500,
                                        fontSize: { xs: "0.7rem", sm: "0.875rem" },
                                      }}
                                    >
                                      {title}
                                    </Typography>
                                  );
                                })()}
                              </TableCell>
                            </TableRow>
                          );
                        })
                      ) : (
                        <TableRow>
                          <TableCell
                            sx={{
                              paddingTop: 4,
                              fontSize: { xs: "0.875rem", sm: "1rem" },
                              fontWeight: 400,
                              color: "#222",
                            }}
                            colSpan={5}
                            align="center"
                          >
                            Không có dữ liệu
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>

              {/* Pagination Section - Responsive */}
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "flex-end",
                  flexDirection: "row",
                  gap: 2,
                  mt: 2,
                  flexWrap: "nowrap",
                }}
              >
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    gap: 1,
                  }}
                >
                  <Typography
                    variant="body2"
                    sx={{
                      fontSize: { xs: "0.75rem", sm: "0.875rem" },
                      textAlign: "center",
                    }}
                  >
                    Số dòng mỗi trang
                  </Typography>
                  <Select
                    value={rowsPerPage}
                    onChange={handleChangeRowsPerPage}
                    size="small"
                    sx={{
                      minWidth: 60,
                      "& .MuiOutlinedInput-notchedOutline": { border: "none" },
                      "& .MuiSelect-select": {
                        padding: "4px 8px",
                        fontSize: { xs: "0.75rem", sm: "0.875rem" },
                      },
                    }}
                  >
                    <MenuItem value={10}>10</MenuItem>
                    <MenuItem value={25}>25</MenuItem>
                    <MenuItem value={50}>50</MenuItem>
                  </Select>
                  <Typography
                    variant="body2"
                    sx={{
                      fontSize: { xs: "0.75rem", sm: "0.875rem" },
                      textAlign: "center",
                    }}
                  >
                    {`${page * rowsPerPage + 1}–${Math.min(
                      (page + 1) * rowsPerPage,
                      totalCount || rows.length
                    )} của ${totalCount || rows.length}`}
                  </Typography>
                </Box>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1, flexShrink: 0 }}>
                  <IconButton
                    disabled={page === 0}
                    onClick={() => handleChangePage(null, page - 1)}
                    size={isMobile ? "small" : "medium"}
                  >
                    <NavigateBeforeIcon />
                  </IconButton>
                  <IconButton
                    disabled={page >= Math.ceil((totalCount || rows.length) / rowsPerPage) - 1}
                    onClick={() => handleChangePage(null, page + 1)}
                    size={isMobile ? "small" : "medium"}
                  >
                    <NavigateNextIcon />
                  </IconButton>
                </Box>
              </Box>
            </Box>
          </Box>
        </Box>
      </Modal>
      <ModalImportMoreCustomerExcel
        open={isOpenModalImportMore}
        setOpen={setIsOpenModalImportMore}
        selectedUserGroup={selectedUserGroup}
      />
    </>
  );
};

export default ModalListCustomerByGroup;
