import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";
import { useSnackbar } from "@/src/hooks/use-snackbar";
import { useStoreId } from "@/src/hooks/use-store-id";
import {
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Tooltip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  useTheme,
  useMediaQuery,
  Card,
  Checkbox,
  FormControl,
  Select,
  MenuItem,
} from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import { tokens } from "@/src/locales/tokens";
import DashboardLayout from "@/src/layouts/dashboard";
import TitleTypography from "@/src/components/title-typography/title-typography";
import { rowPerPageOptionsDefault } from "@/src/types/store/enum-type";
import { Padding } from "@/src/styles/CommonStyle";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { usePathname } from "next/navigation";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import { usePriceList } from "@/src/api/hooks/price-list/use-price-list";
import { GetPriceListParams, PriceListDto } from "@/src/api/services/price-list/price-list.service";
import CustomSwitch from "@/src/components/custom-switch";
import ActionButton from "@/src/components/common/ActionButton";
import { Add, DeleteOutline } from "@mui/icons-material";
import ModalAddPriceList from "./components/modal-add-price-list";
import { useMembershipLevel } from "@/src/api/hooks/membership-level/use-membership-level";
import { MembershipLevel } from "@/src/api/types/membership-level.types";
import ModalManageProductPriceList from "./components/modal-manage-product-price-list";
import TruncatedText from "@/src/components/truncated-text/truncated-text";

const PriceList = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const snackbar = useSnackbar();
  const pathname = usePathname();
  const {
    getListPriceList,
    updatePriceList,
    deletePriceListById,
    deleteManyPriceList,
    setActiveStatusForPriceList,
  } = usePriceList();
  const [priceLists, setPriceLists] = useState<PriceListDto[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [openDialog, setOpenDialog] = useState(false);
  const [priceListDelete, setPriceListDelete] = useState<PriceListDto>(null);
  const [priceListUpdate, setPriceListUpdate] = useState<PriceListDto>(null);

  const storeId = useStoreId();
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [selected, setSelected] = useState<string[]>([]);
  const [openMultipleDeleteDialog, setOpenMultipleDeleteDialog] = useState(false);
  const [isOpenModalAdd, setIsOpenModalAdd] = useState<boolean>(false);
  const [listMembershipLevel, setListMembershipLevel] = useState<MembershipLevel[]>([]);
  const { getMembershipLevel } = useMembershipLevel();
  const [selectedPriceListActive, setSelectedPriceListActive] = useState<PriceListDto>();
  const [priceListManage, setPriceListManage] = useState<PriceListDto>();
  const [isOpenModalActivePriceList, setIsOpenModalActivePriceList] = useState<boolean>(false);
  const [isOpenModalManageProduct, setIsOpenModalManageProduct] = useState<boolean>(false);
  const [isOpenModalListProduct, setIsOpenModalListProduct] = useState<boolean>(false);

  useEffect(() => {
    const fetchMembershipLevel = async () => {
      const res = await getMembershipLevel(0, 1000, storeId);
      setListMembershipLevel(res?.data?.data);
    };
    fetchMembershipLevel();
  }, [storeId]);

  const { permissions } = useAllPermissions();
  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };

  useEffect(() => {
    if (storeId) {
      setIsInitialLoading(true);
      fetchListPriceList().finally(() => {
        setIsInitialLoading(false);
      });
    }
  }, [page, rowsPerPage, storeId]);

  const fetchListPriceList = async () => {
    try {
      const data: GetPriceListParams = {
        shopId: storeId,
        paging: {
          PageIndex: page,
          PageSize: rowsPerPage,
          Name: "Created",
          NameType: "Created",
          Sort: "desc",
          SortType: "desc",
        },
      };
      const response = await getListPriceList(data);
      if (response?.status === 200) {
        setPriceLists(response?.data?.data?.result || []);
        setTotalCount(response?.data?.data?.total || 0);
      }
    } catch (error) {}
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleEdit = (priceListUpdate: PriceListDto) => {
    setPriceListUpdate(priceListUpdate);
    setIsOpenModalAdd(true);
  };

  const handleDelete = (category: any) => {
    setPriceListDelete(category);
    setOpenDialog(true);
  };

  const confirmDelete = async () => {
    try {
      const res = await deletePriceListById(storeId, priceListDelete.priceListId);
      snackbar.success("Xóa bảng giá thành công");

      if (selected.includes(priceListDelete.priceListId)) {
        setSelected(selected.filter((id) => id !== priceListDelete.priceListId));
      }

      setOpenDialog(false);
      setPriceListDelete(null);
      fetchListPriceList();
    } catch (error) {
      snackbar.error("Xóa bảng giá thất bại");
    }
  };

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      setSelected(priceLists.map((item) => item.priceListId));
    } else {
      setSelected([]);
    }
  };

  const handleSelectOne = (event: React.ChangeEvent<HTMLInputElement>, id: string) => {
    if (event?.target?.checked) {
      setSelected([...selected, id]);
    } else {
      setSelected(selected.filter((item) => item !== id));
    }
  };

  const confirmDeleteMultiple = async () => {
    try {
      const res = await deleteManyPriceList(storeId, selected);
      if (res?.status === 200) {
        setPage(0);
        setRowsPerPage(10);
        snackbar.success(`Xóa ${selected.length} bảng giá thành công`);
        setSelected([]);
        setOpenMultipleDeleteDialog(false);
        fetchListPriceList();
      }
    } catch (error) {
      snackbar.error("Xóa bảng giá thất bại");
    }
  };

  function getAdjustmentDisplayValue(
    adjustmentValue: number,
    adjustmentType: string,
    adjustmentUnit: string
  ) {
    if (typeof adjustmentValue !== "number") return "";

    const sign = adjustmentType === "Decrease" ? "-" : "+";

    const unit = adjustmentUnit === "Percentage" ? "%" : " VNĐ";

    const value =
      adjustmentUnit === "Currency" ? adjustmentValue.toLocaleString("vi-VN") : adjustmentValue;

    return `${sign}${value}${unit}`;
  }

  const confirmActivePriceList = async () => {
    const res = await setActiveStatusForPriceList(
      storeId,
      selectedPriceListActive?.priceListId,
      !selectedPriceListActive?.isActive
    );
    if (res?.status === 200) {
      fetchListPriceList();
      setIsOpenModalActivePriceList(false);
    }
  };
  const handleChangeAppliedRanks = async (priceList: PriceListDto, newAppliedRankIds: string[]) => {
    try {
      const res = await updatePriceList({
        ...priceList,
        appliedRankIds: newAppliedRankIds,
        priceListId: priceList.priceListId,
      });
      if (res?.status === 200) {
        snackbar.success("Cập nhật nhóm khách hàng áp dụng thành công");
        fetchListPriceList();
      } else {
        snackbar.error(res?.title || "Cập nhật thất bại");
      }
    } catch (error) {
      snackbar.error("Có lỗi xảy ra");
    }
  };

  return (
    <DashboardLayout>
      <Box sx={{ p: Padding, paddingTop: "20px", mb: 2 }}>
        <Box sx={{ mb: 2 }}>
          <TitleTypography
            sx={{
              fontSize: "20px !important",
              lineHeight: "1",
              fontWeight: "700",
              paddingBottom: "20px",
              marginBottom: "16px",
              borderBottom: "1px solid #bdbdbd",
            }}
          >
            Bảng giá
          </TitleTypography>
        </Box>
        <Box sx={{ display: "flex", justifyContent: "flex-end", mb: 2 }}>
          {selected.length > 0 && (
            <ActionButton
              permission={PERMISSION_TYPE_ENUM.Delete}
              tooltip="Bạn không có quyền xóa"
              startIcon={<DeleteOutline />}
              onClick={() => setOpenMultipleDeleteDialog(true)}
              isGranted={isGranted}
              pathname={pathname}
              size="medium"
              color="error"
              variant="outlined"
              customSx={{
                height: "40px",
                minWidth: "auto",
                px: 2,
                mr: 2,
              }}
            >
              Xoá ({selected?.length})
            </ActionButton>
          )}

          <ActionButton
            permission={PERMISSION_TYPE_ENUM.Add}
            tooltip="Bạn không có quyền tạo mới"
            startIcon={<Add />}
            onClick={() => {
              setIsOpenModalAdd(true);
            }}
            isGranted={isGranted}
            pathname={pathname}
            size="medium"
            color="primary"
            variant="contained"
            customSx={{
              width: { xs: "calc(50% - 4px)", sm: "auto" },
              height: "40px",
              minWidth: "auto",
              px: 2,
            }}
          >
            Thêm bảng giá mới
          </ActionButton>
        </Box>
        <Card sx={{ p: 3 }}>
          <Card>
            <Box
              sx={{
                width: "100%",
                overflowX: "auto",
              }}
            >
              <Table sx={{ minWidth: 850 }}>
                <TableHead>
                  <TableRow>
                    <TableCell padding="checkbox">
                      <Checkbox
                        checked={priceLists.length > 0 && selected.length === priceLists.length}
                        indeterminate={selected.length > 0 && selected.length < priceLists.length}
                        onChange={handleSelectAll}
                      />
                    </TableCell>
                    <TableCell
                      align="center"
                      sx={{
                        minWidth: "40px",
                        whiteSpace: "nowrap",
                      }}
                    >
                      STT
                    </TableCell>
                    <TableCell
                      sx={{
                        minWidth: "80px",
                        whiteSpace: "nowrap",
                      }}
                    >
                      Tên bảng giá
                    </TableCell>
                    <TableCell
                      sx={{
                        minWidth: "200px",
                      }}
                    >
                      Hạng thành viên
                    </TableCell>
                    <TableCell
                      align="center"
                      sx={{
                        minWidth: "150px",
                      }}
                    >
                      Giá trị thay đổi
                    </TableCell>
                    <TableCell
                      align="left"
                      sx={{
                        minWidth: "100px",
                        whiteSpace: "nowrap",
                      }}
                    >
                      Sản phẩm
                    </TableCell>
                    <TableCell
                      align="center"
                      sx={{
                        minWidth: "100px",
                        whiteSpace: "nowrap",
                      }}
                    >
                      Quản lý
                    </TableCell>

                    <TableCell
                      align="center"
                      sx={{
                        minWidth: "100px",
                        whiteSpace: "nowrap",
                      }}
                    >
                      Kích hoạt
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {Array.isArray(priceLists) &&
                    priceLists.map((item, index) => (
                      <TableRow
                        key={item.priceListId}
                        sx={{
                          "&:nth-of-type(odd)": {
                            backgroundColor: theme.palette.action.hover,
                          },
                        }}
                      >
                        <TableCell padding="checkbox">
                          <Checkbox
                            checked={selected.includes(item.priceListId)}
                            onChange={(event) => {
                              // if (item. === 0) {
                              handleSelectOne(event, item.priceListId);
                              // } else {
                              //   snackbar.warning("Danh mục này đã được áp dụng cho sản phẩm");
                              // }
                            }}
                          />
                        </TableCell>
                        <TableCell align="center">{page * rowsPerPage + index + 1}</TableCell>
                        <TableCell>
                          <TruncatedText
                            typographyProps={{ fontSize: 16, fontWeight: 500, width: 400 }}
                            text={item.name}
                          />
                        </TableCell>

                        <TableCell sx={{ maxWidth: 320 }}>
                          <FormControl sx={{ maxWidth: 320 }}>
                            <Select
                              labelId="membership-level-label"
                              multiple
                              sx={{
                                width: 300,
                                height: 45,
                                pr: 4,
                                ".MuiSelect-select": {
                                  height: 45,
                                  display: "flex",
                                  alignItems: "center",
                                  paddingTop: 0,
                                  paddingBottom: 0,
                                },
                              }}
                              value={item.appliedRankIds || []}
                              renderValue={(selected) =>
                                Array.isArray(listMembershipLevel)
                                  ? listMembershipLevel
                                      .filter((level) => selected.includes(level.levelId))
                                      .map((level) => level.levelName)
                                      .join(", ")
                                  : ""
                              }
                              onChange={(e) => {
                                const value = e.target.value as string[];
                                handleChangeAppliedRanks(item, value);
                              }}
                            >
                              {Array.isArray(listMembershipLevel) &&
                                listMembershipLevel.map((level) => (
                                  <MenuItem key={level.levelId} value={level.levelId}>
                                    <Checkbox
                                      checked={item.appliedRankIds?.includes(level.levelId)}
                                    />
                                    <TruncatedText
                                      text={level.levelName}
                                      typographyProps={{ width: 200 }}
                                    />
                                  </MenuItem>
                                ))}
                            </Select>
                          </FormControl>
                        </TableCell>
                        <TableCell
                          sx={{
                            fontWeight: 500,
                            fontSize: 16,
                            color:
                              item.adjustmentType === "Decrease" ? "error.main" : "success.main",
                          }}
                          align="center"
                        >
                          {getAdjustmentDisplayValue(
                            item?.adjustmentValue,
                            item?.adjustmentType,
                            item?.adjustmentUnit
                          )}
                        </TableCell>
                        <TableCell
                          align="left"
                          sx={{
                            cursor: "pointer",
                            transition: "color 0.2s",
                            color: "primary.main",
                            "&:hover": {
                              color: "primary.main",
                            },
                          }}
                          onClick={() => {
                            setIsOpenModalManageProduct(true);
                            setPriceListManage(item);
                          }}
                        >
                          Quản lý sản phẩm
                        </TableCell>
                        <TableCell align="center">
                          <Box sx={{ display: "flex", justifyContent: "center", gap: 1 }}>
                            <Tooltip
                              title={
                                !isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)
                                  ? "Bạn không có quyền sửa"
                                  : "Sửa bảng giá"
                              }
                            >
                              <span>
                                <IconButton
                                  onClick={() => handleEdit(item)}
                                  size="small"
                                  sx={{ color: "primary.main" }}
                                  disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
                                >
                                  <EditIcon fontSize="small" />
                                </IconButton>
                              </span>
                            </Tooltip>
                            <Tooltip
                              title={
                                !isGranted(pathname, PERMISSION_TYPE_ENUM.Delete)
                                  ? "Bạn không có quyền xoá"
                                  : "Xoá bảng giá"
                              }
                            >
                              <span>
                                <IconButton
                                  onClick={() => handleDelete(item)}
                                  size="small"
                                  sx={{ color: "error.main" }}
                                  disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Delete)}
                                >
                                  <DeleteIcon fontSize="small" />
                                </IconButton>
                              </span>
                            </Tooltip>
                          </Box>
                        </TableCell>
                        <TableCell align="center">
                          <CustomSwitch
                            checked={item.isActive}
                            value={item.isActive}
                            onClick={() => {
                              setSelectedPriceListActive(item);
                              setIsOpenModalActivePriceList(true);
                            }}
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </Box>
            <TablePagination
              labelRowsPerPage="Số hàng mỗi trang"
              component="div"
              count={totalCount}
              page={page}
              onPageChange={handleChangePage}
              rowsPerPage={rowsPerPage}
              onRowsPerPageChange={handleChangeRowsPerPage}
              rowsPerPageOptions={rowPerPageOptionsDefault}
            />
          </Card>
        </Card>

        <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
          <DialogTitle>{t(tokens.contentManagement.category.delete.confirmTitle)}</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Bạn có chắc chắn muốn xóa bảng giá {""}
              <strong> {priceListDelete?.name}</strong>
              {t(tokens.contentManagement.priceList.delete.confirmMessageSuffix || " không?")}
            </DialogContentText>
          </DialogContent>
          <DialogActions sx={{ mx: 2, my: 2 }}>
            <Button onClick={() => setOpenDialog(false)} variant="outlined" sx={{ minWidth: 100 }}>
              {t(tokens.common.cancel)}
            </Button>
            <Button
              onClick={confirmDelete}
              color="error"
              variant="contained"
              autoFocus
              sx={{ minWidth: 100 }}
            >
              {t(tokens.common.delete)}
            </Button>
          </DialogActions>
        </Dialog>

        <Dialog
          open={openMultipleDeleteDialog}
          onClose={() => setOpenMultipleDeleteDialog(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Xác nhận xóa nhiều bảng giá</DialogTitle>
          <DialogContent>
            <DialogContentText>
              {`Bạn có chắc chắn muốn xóa ${selected.length} bảng giá đã chọn không?`}
            </DialogContentText>
          </DialogContent>
          <DialogActions sx={{ mx: 2, my: 2 }}>
            <Button
              onClick={() => setOpenMultipleDeleteDialog(false)}
              variant="outlined"
              sx={{ minWidth: 100 }}
            >
              Hủy
            </Button>
            <Button
              onClick={confirmDeleteMultiple}
              color="error"
              variant="contained"
              autoFocus
              sx={{ minWidth: 100 }}
            >
              Xóa
            </Button>
          </DialogActions>
        </Dialog>

        <Dialog
          open={isOpenModalActivePriceList}
          onClose={() => setIsOpenModalActivePriceList(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>{`Xác nhận ${
            selectedPriceListActive?.isActive ? "vô hiệu hoá" : "kích hoạt"
          }  bảng giá`}</DialogTitle>
          <DialogContent>
            <DialogContentText>
              {`Bạn có chắc chắn muốn ${
                selectedPriceListActive?.isActive ? "vô hiệu hoá" : "kích hoạt"
              } bảng giá `}
              <strong>{selectedPriceListActive?.name}</strong>
              {` không?`}
            </DialogContentText>
          </DialogContent>
          <DialogActions sx={{ mx: 2, my: 2 }}>
            <Button
              onClick={() => setIsOpenModalActivePriceList(false)}
              variant="outlined"
              sx={{ minWidth: 100 }}
            >
              Hủy
            </Button>
            <Button
              onClick={confirmActivePriceList}
              variant="contained"
              autoFocus
              sx={{ minWidth: 100 }}
            >
              Xác nhận
            </Button>
          </DialogActions>
        </Dialog>

        <ModalAddPriceList
          isOpenModalAdd={isOpenModalAdd}
          setIsOpenModalAdd={setIsOpenModalAdd}
          onClose={() => setIsOpenModalAdd(false)}
          listMembershipLevel={listMembershipLevel}
          fetchListPriceList={fetchListPriceList}
          priceListUpdate={priceListUpdate}
          setPriceListUpdate={setPriceListUpdate}
        />

        <ModalManageProductPriceList
          onClose={() => setIsOpenModalManageProduct(false)}
          isOpenModalListProduct={isOpenModalListProduct}
          setIsOpenModalListProduct={setIsOpenModalListProduct}
          isOpenModalManageProduct={isOpenModalManageProduct}
          setIsOpenModalManageProduct={setIsOpenModalManageProduct}
          priceListManage={priceListManage}
        />
      </Box>
    </DashboardLayout>
  );
};

export default PriceList;
