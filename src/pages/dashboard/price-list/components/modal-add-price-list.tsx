import React, { useEffect } from "react";
import {
  Box,
  TextField,
  Select,
  MenuItem,
  Checkbox,
  Button,
  Grid,
  FormControl,
  Typography,
  OutlinedInput,
  IconButton,
  RadioGroup,
  FormControlLabel,
  Radio,
  Dialog,
  Tooltip,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useFormik } from "formik";
import * as Yup from "yup";
import { MembershipLevel } from "@/src/api/types/membership-level.types";
import { usePriceList } from "@/src/api/hooks/price-list/use-price-list";
import {
  CreatePriceListRequest,
  PriceListDto,
} from "@/src/api/services/price-list/price-list.service";
import { useStoreId } from "@/src/hooks/use-store-id";
import useSnackbar from "@/src/hooks/use-snackbar";
import { ErrorOutline } from "@mui/icons-material";
import TruncatedText from "@/src/components/truncated-text/truncated-text";
export interface ModalAddPriceListProps {
  isOpenModalAdd?: boolean;
  setIsOpenModalAdd?: any;
  listMembershipLevel: MembershipLevel[];
  onClose: () => void;
  fetchListPriceList: () => Promise<void>;
  priceListUpdate: PriceListDto;
  setPriceListUpdate?: any;
}

const adjustmentTypes = [
  { value: "Increase", label: "Tăng" },
  { value: "Decrease", label: "Giảm" },
];

const adjustmentUnits = [
  { value: "Percentage", label: "Tỷ lệ (%)" },
  { value: "Currency", label: "Giá tiền (VNĐ)" },
];

const validationSchema = Yup.object({
  name: Yup.string()
    .required("Vui lòng nhập tên bảng giá")
    .max(255, "Tên bảng giá không được vượt quá 255 ký tự"),
  appliedRanks: Yup.array().min(1, "Chọn ít nhất 1 hạng áp dụng"),
  adjustmentType: Yup.string().required("Chọn loại điều chỉnh"),
  adjustmentValue: Yup.number()
    .typeError("Giá trị phải là số")
    .required("Vui lòng nhập giá trị thay đổi"),
  adjustmentUnit: Yup.string().required("Chọn đơn vị"),
});

const ModalAddPriceList: React.FC<ModalAddPriceListProps> = ({
  isOpenModalAdd,
  setIsOpenModalAdd,
  listMembershipLevel,
  onClose,
  fetchListPriceList,
  priceListUpdate,
  setPriceListUpdate,
}) => {
  const { createPriceList, updatePriceList } = usePriceList();
  const storeId = useStoreId();
  const snackbar = useSnackbar();
  const getInitialValues = () => {
    if (priceListUpdate) {
      return {
        name: priceListUpdate.name,
        appliedRanks: priceListUpdate.appliedRankIds,
        adjustmentType: priceListUpdate.adjustmentType,
        adjustmentValue: priceListUpdate.adjustmentValue.toString(),
        adjustmentUnit: priceListUpdate.adjustmentUnit,
      };
    }
    return {
      name: "",
      appliedRanks: [],
      adjustmentType: "",
      adjustmentValue: "",
      adjustmentUnit: "",
    };
  };
  const formik = useFormik({
    initialValues: getInitialValues(),
    validationSchema,
    onSubmit: async (values) => {
      const data: CreatePriceListRequest = {
        shopId: storeId,
        adjustmentType: values.adjustmentType,
        adjustmentUnit: values.adjustmentUnit,
        appliedRankIds: values.appliedRanks,
        adjustmentValue: Number(values.adjustmentValue),
        name: values.name,
      };
      if (priceListUpdate && priceListUpdate?.priceListId) {
        const res = await updatePriceList({ ...data, priceListId: priceListUpdate.priceListId });
        if (res?.status === 200) {
          fetchListPriceList();
          snackbar.success("Cập nhật bảng giá thành công");
          setPriceListUpdate(null);
          formik.resetForm();
          onClose();
        } else if (res?.status === 400) {
          snackbar.error(res?.title);
        }
      } else {
        const res = await createPriceList(data);
        if (res?.status === 200) {
          fetchListPriceList();
          snackbar.success("Thêm mới bảng giá thành công");
          setPriceListUpdate(null);
          formik.resetForm();
          onClose();
        } else if (res?.status === 400) {
          snackbar.error(res?.title);
        }
      }
    },
    enableReinitialize: true,
  });

  return (
    <Dialog
      open={isOpenModalAdd}
      onClose={() => {
        formik.resetForm();
        setPriceListUpdate(null);
        setIsOpenModalAdd(false);
      }}
      maxWidth="lg"
      fullWidth
    >
      <Box
        sx={{
          bgcolor: "#fff",
          borderRadius: 2,
          p: 3,
          boxShadow: 3,
          position: "relative",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
          <Typography variant="h6" sx={{ flex: 1, fontWeight: 700 }}>
            Thêm bảng giá mới
          </Typography>
          <IconButton onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </Box>

        <form onSubmit={formik.handleSubmit} autoComplete="off">
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} md={6}>
              <Typography sx={{ display: "flex", mb: "8px" }}>
                Tên bảng giá <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
              </Typography>
              <FormControl fullWidth error={formik.touched.name && Boolean(formik.errors.name)}>
                <TextField
                  name="name"
                  fullWidth
                  value={formik.values.name}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.name && Boolean(formik.errors.name)}
                  helperText={
                    formik.touched.name && typeof formik.errors.name === "string"
                      ? formik.errors.name
                      : ""
                  }
                  sx={{
                    height: 45,
                    "& .MuiInputBase-root": {
                      height: 45,
                      display: "flex",
                      alignItems: "center",
                      paddingTop: 0,
                      paddingBottom: 0,
                    },
                    "& input": {
                      height: 45,
                      boxSizing: "border-box",
                      paddingTop: 0,
                      paddingBottom: 0,
                      display: "flex",
                      alignItems: "center",
                    },
                  }}
                />
                {formik.touched.name && typeof formik.errors.name === "string" && (
                  <Typography variant="caption" color="error">
                    {formik.errors.name}
                  </Typography>
                )}
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography id="applied-rank-label" sx={{ display: "flex", mb: "8px" }}>
                Hạng áp dụng<Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
              </Typography>
              <FormControl
                fullWidth
                error={formik.touched.appliedRanks && Boolean(formik.errors.appliedRanks)}
              >
                <Select
                  labelId="applied-rank-label"
                  multiple
                  name="appliedRanks"
                  value={formik.values.appliedRanks}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  input={<OutlinedInput label="Hạng áp dụng" />}
                  renderValue={(selected) =>
                    Array.isArray(listMembershipLevel) &&
                    listMembershipLevel
                      .filter((level) => selected.includes(level.levelId))
                      .map((level) => level.levelName)
                      .join(", ")
                  }
                  sx={{
                    height: 45,
                    ".MuiSelect-select": {
                      height: 45,
                      display: "flex",
                      alignItems: "center",
                      paddingTop: 0,
                      paddingBottom: 0,
                      width: "85%",
                    },
                  }}
                >
                  {Array.isArray(listMembershipLevel) &&
                    listMembershipLevel.map((level) => (
                      <MenuItem key={level.levelId} value={level.levelId} sx={{ width: 570 }}>
                        <Checkbox checked={formik.values.appliedRanks.includes(level.levelId)} />
                        <TruncatedText text={level.levelName} />
                      </MenuItem>
                    ))}
                </Select>
                {formik.touched.appliedRanks && typeof formik.errors.appliedRanks === "string" && (
                  <Typography variant="caption" color="error">
                    {formik.errors.appliedRanks}
                  </Typography>
                )}
              </FormControl>
            </Grid>
          </Grid>

          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} md={3}>
              <Typography sx={{ mb: 1, display: "flex" }}>
                Loại điều chỉnh <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
              </Typography>
              <FormControl
                fullWidth
                error={formik.touched.adjustmentType && Boolean(formik.errors.adjustmentType)}
              >
                <Select
                  name="adjustmentType"
                  value={formik.values.adjustmentType}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  displayEmpty
                  sx={{
                    height: 45,
                    ".MuiSelect-select": {
                      height: 45,
                      display: "flex",
                      alignItems: "center",
                      paddingTop: 0,
                      paddingBottom: 0,
                    },
                  }}
                >
                  <MenuItem value="" disabled>
                    Chọn loại điều chỉnh
                  </MenuItem>
                  {adjustmentTypes.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.label}
                    </MenuItem>
                  ))}
                </Select>
                {formik.touched.adjustmentType &&
                  typeof formik.errors.adjustmentType === "string" && (
                    <Typography variant="caption" color="error">
                      {formik.errors.adjustmentType}
                    </Typography>
                  )}
              </FormControl>
            </Grid>

            <Grid item xs={12} md={5}>
              <Typography sx={{ mb: 1, display: "flex" }}>
                Giá trị thay đổi <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
                <Tooltip title="Áp dụng cho giá bán" placement="top">
                  <Typography sx={{ ml: 1 }}>
                    <ErrorOutline
                      sx={{
                        fontSize: 18,
                        cursor: "pointer",
                        mb: "4px",
                        color: "rgb(206, 166, 73)",
                      }}
                    />
                  </Typography>
                </Tooltip>
              </Typography>
              <FormControl
                fullWidth
                error={formik.touched.adjustmentValue && Boolean(formik.errors.adjustmentValue)}
              >
                <TextField
                  name="adjustmentValue"
                  fullWidth
                  value={formik.values.adjustmentValue}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.adjustmentValue && Boolean(formik.errors.adjustmentValue)}
                  helperText={
                    formik.touched.adjustmentValue &&
                    typeof formik.errors.adjustmentValue === "string"
                      ? formik.errors.adjustmentValue
                      : ""
                  }
                  sx={{
                    height: 45,
                    "& .MuiInputBase-root": {
                      height: 45,
                      display: "flex",
                      alignItems: "center",
                      paddingTop: 0,
                      paddingBottom: 0,
                    },
                    "& input": {
                      height: 45,
                      boxSizing: "border-box",
                      paddingTop: 0,
                      paddingBottom: 0,
                      display: "flex",
                      alignItems: "center",
                    },
                  }}
                />
                {formik.touched.adjustmentValue &&
                  typeof formik.errors.adjustmentValue === "string" && (
                    <Typography variant="caption" color="error">
                      {formik.errors.adjustmentValue}
                    </Typography>
                  )}
              </FormControl>
            </Grid>

            <Grid item xs={12} md={4}>
              <Typography sx={{ mb: 1, display: "flex" }}>
                Đơn vị <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
              </Typography>
              <FormControl
                fullWidth
                error={formik.touched.adjustmentUnit && Boolean(formik.errors.adjustmentUnit)}
              >
                <Select
                  name="adjustmentUnit"
                  value={formik.values.adjustmentUnit}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  displayEmpty
                  sx={{
                    height: 45,
                    ".MuiSelect-select": {
                      height: 45,
                      display: "flex",
                      alignItems: "center",
                      paddingTop: 0,
                      paddingBottom: 0,
                    },
                  }}
                >
                  <MenuItem value="" disabled>
                    Chọn đơn vị
                  </MenuItem>
                  {adjustmentUnits.map((unit) => (
                    <MenuItem key={unit.value} value={unit.value}>
                      {unit.label}
                    </MenuItem>
                  ))}
                </Select>
                {formik.touched.adjustmentUnit &&
                  typeof formik.errors.adjustmentUnit === "string" && (
                    <Typography variant="caption" color="error">
                      {formik.errors.adjustmentUnit}
                    </Typography>
                  )}
              </FormControl>
            </Grid>
          </Grid>

          <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2 }}>
            <Button
              variant="outlined"
              color="primary"
              sx={{ minWidth: 120, height: 45, mr: 2 }}
              onClick={() => {
                formik.resetForm();
                setPriceListUpdate(null);
                onClose();
              }}
              type="button"
            >
              Huỷ
            </Button>
            <Button
              variant="contained"
              color="primary"
              sx={{ minWidth: 120, height: 45 }}
              type="submit"
            >
              Lưu
            </Button>
          </Box>
        </form>
      </Box>
    </Dialog>
  );
};

export default ModalAddPriceList;
