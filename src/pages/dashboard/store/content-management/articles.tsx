import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";
import { useSnackbar } from "@/src/hooks/use-snackbar";
import { useStoreId } from "@/src/hooks/use-store-id";
import { paths } from "@/src/paths";
import {
  Box,
  Button,
  Checkbox,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TextField,
  TablePagination,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Snackbar,
  Typography,
  Chip,
  Tooltip,
  InputAdornment,
} from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import LinkIcon from "@mui/icons-material/Link";
import DeleteIcon from "@mui/icons-material/Delete";
import AddIcon from "@mui/icons-material/Add";
import { tokens } from "@/src/locales/tokens";
import { formatDateTimeDisplay } from "@/src/utils/date-utils";
import ShowImage from "@/src/components/show-image";
import { useArticle } from "@/src/api/hooks/dashboard/store/use-article";
import { EmptyState } from "@/src/components/common/empty-state";
import { LoadingState } from "@/src/components/common/loading-state";
import { rowPerPageOptionsDefault } from "@/src/types/store/enum-type";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { usePathname } from "next/navigation";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import { useDebounce } from "@/src/hooks/use-debounce";
import dayjs from "dayjs";
import { ArticleDto } from "@/src/api/services/dashboard/store/article.service";
import TruncatedText from "@/src/components/truncated-text/truncated-text";
import { Search } from "@mui/icons-material";

const Articles = () => {
  const { t } = useTranslation();
  const pathname = usePathname();
  const snackbar = useSnackbar();
  const { getArticle, deleteArticle, loading } = useArticle();
  const [selected, setSelected] = useState<string[]>([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openMultipleDeleteDialog, setOpenMultipleDeleteDialog] = useState(false);
  const [articleToDelete, setArticleToDelete] = useState<any>(null);
  const router = useRouter();
  const [articles, setArticles] = useState<ArticleDto[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [searchTerm, setSearchTerm] = useState("");
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const storeId = useStoreId();
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const { permissions } = useAllPermissions();
  const debouncedSearchValue = useDebounce(searchTerm, 500);
  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };
  useEffect(() => {
    if (storeId) {
      setIsInitialLoading(true);
      fetchArticles().finally(() => {
        setIsInitialLoading(false);
      });
    }
  }, [page, rowsPerPage, debouncedSearchValue, storeId]);

  const fetchArticles = async () => {
    try {
      const response = await getArticle(page * rowsPerPage, rowsPerPage, debouncedSearchValue);
      setArticles(response.data.data || []);
      setTotalCount(response.data.total || 0);
    } catch (error) {
      snackbar.error(t("common.errorOccurred"));
    }
  };

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      setSelected(articles.map((article) => article.articleId));
    } else {
      setSelected([]);
    }
  };

  const handleSelectOne = (event: React.ChangeEvent<HTMLInputElement>, id: string) => {
    if (event.target.checked) {
      setSelected([...selected, id]);
    } else {
      setSelected(selected.filter((item) => item !== id));
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleAddNew = () => {
    router.push(paths.dashboard.store.createArticle);
  };

  const handleEdit = (article) => {
    router.push({
      pathname: paths.dashboard.store.createArticle,
      query: { articleId: article.articleId },
    });
  };

  const handleCopyLink = (article) => {
    navigator.clipboard.writeText(
      `${window.location.origin}/${paths.dashboard.store.createArticle}?articleId=${article.articleId}`
    );
    setSnackbarOpen(true);
  };

  const handleDelete = (article: any) => {
    setArticleToDelete(article);
    setOpenDeleteDialog(true);
  };

  const confirmDelete = async () => {
    try {
      await deleteArticle(articleToDelete.articleId);
      snackbar.success(t(tokens.contentManagement.article.snackbar.deleteSuccess));

      if (selected.includes(articleToDelete.articleId)) {
        setSelected(selected.filter((id) => id !== articleToDelete.articleId));
      }

      setOpenDeleteDialog(false);
      setArticleToDelete(null);
      fetchArticles();
    } catch (error) {
      snackbar.error(t(tokens.contentManagement.article.snackbar.deleteError));
    }
  };

  const handleDeleteMultiple = () => {
    setOpenMultipleDeleteDialog(true);
  };

  const confirmDeleteMultiple = async () => {
    try {
      await Promise.all(selected.map((id) => deleteArticle(id)));
      snackbar.success(`Xóa ${selected.length} bài viết thành công`);
      setSelected([]);
      setOpenMultipleDeleteDialog(false);
      fetchArticles();
    } catch (error) {
      snackbar.error("Xóa bài viết thất bại");
    }
  };

  if (!storeId) {
    return null;
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          flexDirection: { xs: "column", sm: "row" },
          mb: 3,
        }}
      >
        <Box sx={{ display: "flex", gap: 2 }}>
          <Tooltip
            title={!isGranted(pathname, PERMISSION_TYPE_ENUM.Add) ? "Bạn không có quyền thêm" : ""}
          >
            <span>
              <Button
                sx={{ background: "#2654FE", textTransform: "none", mb: { xs: 1, sm: 0 } }}
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleAddNew}
                disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Add)}
              >
                {t(tokens.contentManagement.article.addNew)}
              </Button>
            </span>
          </Tooltip>

          {selected.length > 0 && (
            <Tooltip
              title={
                !isGranted(pathname, PERMISSION_TYPE_ENUM.Delete) ? "Bạn không có quyền xoá" : ""
              }
            >
              <span>
                <Button
                  variant="outlined"
                  color="error"
                  startIcon={<DeleteIcon />}
                  onClick={handleDeleteMultiple}
                  disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Delete)}
                >
                  {t(tokens.common.delete)} ({selected.length})
                </Button>
              </span>
            </Tooltip>
          )}
        </Box>

        <TextField
          placeholder={t(tokens.contentManagement.article.search)}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          variant="outlined"
          size="small"
          sx={{ width: 300 }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      {(loading && page === 0) || isInitialLoading ? (
        <LoadingState />
      ) : !isInitialLoading && articles.length === 0 ? (
        <EmptyState
          title={t(tokens.contentManagement.article.emptyState.title)}
          subtitle={t(tokens.contentManagement.article.emptyState.subtitle)}
          buttonText={t(tokens.contentManagement.article.emptyState.button)}
          onAddClick={handleAddNew}
          isGranted={isGranted}
        />
      ) : (
        <>
          <Box sx={{ overflowX: "auto" }}>
            <Table sx={{ minWidth: 1000 }}>
              <TableHead>
                <TableRow>
                  <TableCell padding="checkbox" width="5%">
                    <Checkbox
                      checked={articles.length > 0 && selected.length === articles.length}
                      indeterminate={selected.length > 0 && selected.length < articles.length}
                      onChange={handleSelectAll}
                    />
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>STT</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Tiêu đề</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Hình ảnh</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Ngày tạo</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Trạng thái</TableCell>
                  <TableCell align="center" sx={{ fontWeight: 600 }}>
                    Thao tác
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {articles.map((article, index) => (
                  <TableRow key={article.articleId}>
                    <TableCell padding="checkbox">
                      <Checkbox
                        checked={selected.includes(article.articleId)}
                        onChange={(event) => handleSelectOne(event, article.articleId)}
                      />
                    </TableCell>
                    <TableCell>{page * rowsPerPage + index + 1}</TableCell>
                    <TableCell sx={{ width: 450 }}>
                      <TruncatedText typographyProps={{ width: 400 }} text={article.title} />
                    </TableCell>
                    <TableCell>
                      <ShowImage
                        src={article?.images[0]?.link}
                        alt={article.title}
                        size={50}
                        circular={true}
                      />
                    </TableCell>
                    <TableCell>{dayjs(article.created).format("DD/MM/YYYY HH:mm:ss")}</TableCell>
                    <TableCell>
                      <Chip
                        label={article.typePublish === "Publish" ? "Xuất bản" : "Không xuất bản"}
                        color={article.typePublish === "Publish" ? "success" : "default"}
                        sx={{
                          backgroundColor:
                            article.typePublish === "Publish"
                              ? "rgba(84, 214, 44, 0.16)"
                              : "rgba(145, 158, 171, 0.16)",
                          color:
                            article.typePublish === "Publish"
                              ? "rgb(34, 154, 22)"
                              : "rgb(99, 115, 129)",
                        }}
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Box sx={{ display: "flex", justifyContent: "center", gap: 1 }}>
                        <Tooltip
                          title={
                            !isGranted(pathname, PERMISSION_TYPE_ENUM.Delete)
                              ? "Bạn không có quyền sửa"
                              : "Sửa bài viết"
                          }
                        >
                          <span>
                            <IconButton
                              onClick={() => handleEdit(article)}
                              size="small"
                              sx={{ color: "primary.main" }}
                              disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </span>
                        </Tooltip>
                        <Tooltip title="Sao chép đường dẫn">
                          <IconButton onClick={() => handleCopyLink(article)} size="small">
                            <LinkIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip
                          title={
                            !isGranted(pathname, PERMISSION_TYPE_ENUM.Delete)
                              ? "Bạn không có quyền xoá"
                              : "Xoá bài viết"
                          }
                        >
                          <span>
                            <IconButton
                              onClick={() => handleDelete(article)}
                              size="small"
                              sx={{ color: "error.main" }}
                              disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Delete)}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </span>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </Box>
          <TablePagination
            labelRowsPerPage="Số hàng mỗi trang"
            component="div"
            count={totalCount}
            page={page}
            onPageChange={handleChangePage}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            rowsPerPageOptions={rowPerPageOptionsDefault}
          />
        </>
      )}

      <Dialog
        open={openDeleteDialog}
        onClose={() => setOpenDeleteDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Xác nhận xóa</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {`Bạn có chắc chắn muốn xóa bài viết "${articleToDelete?.title || ""}" không?`}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setOpenDeleteDialog(false)}
            variant="outlined"
            sx={{ minWidth: 100 }}
          >
            Hủy
          </Button>
          <Button
            onClick={confirmDelete}
            color="error"
            variant="contained"
            autoFocus
            sx={{ minWidth: 100 }}
          >
            Xóa
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={openMultipleDeleteDialog}
        onClose={() => setOpenMultipleDeleteDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Xác nhận xóa nhiều bài viết</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {`Bạn có chắc chắn muốn xóa ${selected.length} bài viết đã chọn không?`}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setOpenMultipleDeleteDialog(false)}
            variant="outlined"
            sx={{ minWidth: 100 }}
          >
            Hủy
          </Button>
          <Button
            onClick={confirmDeleteMultiple}
            color="error"
            variant="contained"
            autoFocus
            sx={{ minWidth: 100 }}
          >
            Xóa
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setSnackbarOpen(false)}
        message={t(tokens.contentManagement.article.snackbar.copySuccess)}
      />
    </Box>
  );
};

export default Articles;
