"use client";
import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Button,
  Card,
  TextField,
  Typography,
  Grid,
  CircularProgress,
  IconButton,
  InputAdornment,
  debounce,
} from "@mui/material";
import DashboardLayout from "../../../layouts/dashboard";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useSnackbar } from "@/src/hooks/use-snackbar";
import { useTranslation } from "react-i18next";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import ShareIcon from "@mui/icons-material/Share";
import ShareStoreDialog from "./share-store-dialog";
import { useShop } from "@/src/api/hooks/shop/use-shop";
import { useStoreId } from "@/src/hooks/use-store-id";
import { StorageService } from "nextjs-api-lib";
import TitleTypography from "@/src/components/title-typography/title-typography";
import { useZaloAuth } from "@/src/api/hooks/zalo/use-zalo-auth";
import dynamic from "next/dynamic";
import { Padding } from "@/src/styles/CommonStyle";
import { useRouter } from "next/router";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { usePathname } from "next/navigation";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import LogoUpload from "@/src/components/logo-upload";
import { CreateFileGroupRequest, GetGroupFileRequest, RefType } from "@/src/api/types/media.types";
import { useMedia } from "@/src/api/hooks/media/use-media";
import { FileType } from "@/src/constants/file-types";
import { useAppDispatch } from "@/src/redux/hooks";
import { updateShop } from "@/src/redux/slices/shopSlice";
import { stripHtmlAndSpaces } from "@/src/components/react-quill-editor";
import DOMPurify from "dompurify";

const ReactQuillEditor = dynamic(() => import("@/src/components/react-quill-editor"), {
  ssr: false,
  loading: () => <p>Loading editor...</p>,
});

const GeneralSettings = ({ onChange: parentOnChange, initialData }) => {
  const dispatch = useAppDispatch();
  const { t } = useTranslation();
  const pathname = usePathname();
  const snackbar = useSnackbar();
  const [isLoading, setIsLoading] = useState(false);
  const [shareDialogOpen, setShareDialogOpen] = useState(false);
  const [showSecretKey, setShowSecretKey] = useState(false);
  const storeId = useStoreId();
  const partnerId = StorageService.get("partnerId") || null;
  const { detailShop, updateLogo } = useShop();
  const {
    fetchZaloAuthUrl,
    loading: isAuthorizing,
    error: authError,
    setError: setAuthError,
  } = useZaloAuth(); // Use the hook
  const [editorData, setEditorData] = useState(initialData || "");
  const [shopLogo, setShopLogo] = useState<File | null>(null);
  const [validateLogoError, setValidateLogoError] = useState(false);
  const [defaultGroupId, setDefaultGroupId] = useState<string>("");
  const { getGroups, uploadFile } = useMedia();

  const url = useRouter();
  const { permissions } = useAllPermissions();
  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };

  useEffect(() => {
    const fetchDefaultGroup = async () => {
      try {
        const data: GetGroupFileRequest = {
          ShopId: storeId,
          Skip: 0,
          Limit: 1,
        };
        const response = await getGroups(data);
        if (response.data?.data?.length > 0) {
          setDefaultGroupId(response.data.data[0].groupFileId);
        }
      } catch (error) {}
    };
    fetchDefaultGroup();
  }, [storeId]);
  const validationSchema = Yup.object({
    storeName: Yup.string()
      .required("Tên cửa hàng là bắt buộc")
      .max(255, "Tên cửa hàng không được vượt quá 255 ký tự"),
    slogan: Yup.string().max(255, "Slogan không được vượt quá 255 ký tự"),
    businessInfo: Yup.string().test(
      "max-length-without-html-and-spaces",
      "Thông tin giới thiệu không được vượt quá 4000 ký tự",
      function (value) {
        const textOnly = stripHtmlAndSpaces(value);
        return textOnly.length <= 4000;
      }
    ),
  });

  const formik = useFormik({
    initialValues: {
      storeName: "",
      slogan: "",
      referralCode: "",
      logo: null,
      businessInfo: "",
      shopLogo: null,
      shopUrl: "",
      logoUrl: "",
      oaId: "",
      zaloAppId: "",
      zaloSecretKey: "",
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        setIsLoading(true);
        const getLogoData = async () => {
          if (!shopLogo) return values.shopLogo;

          const data: CreateFileGroupRequest = {
            FileUpload: shopLogo,
            ShopId: storeId,
            GroupFileId: defaultGroupId,
            RefType: RefType.Shop,
            RefId: "",
          };

          const res = await uploadFile(data);

          return res?.status === 200
            ? {
                link: res?.data?.link,
                mediaFileId: res?.data?.mediaFileId,
                type: FileType.IMAGE,
              }
            : null;
        };

        const logoData = await getLogoData();

        await dispatch(
          updateShop({
            shopId: storeId,
            partnerId: partnerId,
            shopName: values.storeName,
            shopSlogan: values.slogan,
            referralCode: values.referralCode,
            shopInfo: DOMPurify.sanitize(editorData),
            shopLogo: logoData,
            logo: undefined,
            oaId: values.oaId,
            zaloAppId: values.zaloAppId,
            zaloSecretKey: values.zaloSecretKey,
          })
        );

        snackbar.success("Cập nhật thông tin thành công");
      } catch (error) {
        snackbar.error("Có lỗi xảy ra, vui lòng thử lại");
      } finally {
        setIsLoading(false);
      }
    },
  });

  useEffect(() => {
    const fetchShopDetail = async () => {
      if (storeId) {
        try {
          setIsLoading(true);
          const detail = await detailShop(storeId);
          const shopDetail = detail?.data as any;
          formik.setValues({
            storeName: shopDetail?.shopName || "",
            slogan: shopDetail?.shopSlogan || "",
            referralCode: shopDetail?.prefixCode || "",
            logo: null,
            shopLogo: shopDetail?.shopLogo,
            logoUrl: shopDetail?.shopLogo?.link || "",
            businessInfo: shopDetail?.shopInfo || "",
            shopUrl: shopDetail?.shopDeeplink || "",
            oaId: shopDetail?.oaId || "",
            zaloAppId: shopDetail?.zaloAppId || "",
            zaloSecretKey: shopDetail?.zaloSecretKey || "",
          });
          setEditorData(shopDetail?.shopInfo || "");
        } catch (error) {
          console.error("Error fetching shop details:", error);
          snackbar.error("Không thể tải thông tin cửa hàng");
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchShopDetail();
  }, [storeId]);

  const handleLogoChange = (file: File | null) => {
    formik.setFieldValue("logo", file);
  };

  const handleCopyUrl = () => {
    navigator.clipboard.writeText(formik.values.shopUrl);
    snackbar.success("Đã sao chép link cửa hàng");
  };

  const debouncedSetFieldValue = useCallback(
    debounce((newData) => {
      formik.setFieldValue("businessInfo", newData);
      if (parentOnChange) {
        parentOnChange(newData);
      }
    }, 500),
    [formik.setFieldValue, parentOnChange]
  );

  const handleEditorChange = (newData) => {
    setEditorData(newData);
    debouncedSetFieldValue(newData);
  };

  return (
    <DashboardLayout>
      <Box sx={{ padding: Padding, paddingTop: "20px" }}>
        <TitleTypography
          sx={{
            fontSize: "20px !important",
            lineHeight: "1",
            fontWeight: "700",
            paddingBottom: "20px",
            marginBottom: "16px",
            borderBottom: "1px solid #bdbdbd",
          }}
        >
          Thông tin cửa hàng
        </TitleTypography>
        <Card sx={{ p: 2, mt: 2 }}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Box sx={{ mb: 3 }}>
                <Typography sx={{ fontSize: 15, fontWeight: 500, mb: 0.5 }}>
                  Link cửa hàng
                </Typography>
                <TextField
                  fullWidth
                  value={formik.values.shopUrl}
                  InputProps={{
                    readOnly: true,
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton onClick={handleCopyUrl}>
                          <ContentCopyIcon />
                        </IconButton>
                        <IconButton onClick={() => setShareDialogOpen(true)}>
                          <ShareIcon />
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
              </Box>

              <Box>
                <Typography sx={{ fontSize: 15, fontWeight: 500, mb: 0.5, display: "flex" }}>
                  Tên cửa hàng <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
                </Typography>
                <TextField
                  fullWidth
                  name="storeName"
                  value={formik.values.storeName}
                  onChange={formik.handleChange}
                  error={!!(formik.touched.storeName && formik.errors.storeName)}
                  helperText={formik.touched.storeName && formik.errors.storeName}
                  sx={{ mb: 3 }}
                />
              </Box>

              <Box>
                <Typography sx={{ fontSize: 15, fontWeight: 500, mb: 0.5, display: "flex" }}>
                  Slogan
                </Typography>
                <TextField
                  fullWidth
                  name="slogan"
                  value={formik.values.slogan}
                  onChange={formik.handleChange}
                  error={!!(formik.touched.slogan && formik.errors.slogan)}
                  helperText={formik.touched.slogan && formik.errors.slogan}
                  sx={{ mb: 3 }}
                />
              </Box>

              <Box>
                <Typography sx={{ fontSize: 15, fontWeight: 500, mb: 0.5, display: "flex" }}>
                  Zalo OA ID
                </Typography>
                <TextField
                  fullWidth
                  name="oaId"
                  value={formik.values.oaId}
                  onChange={formik.handleChange}
                  error={!!(formik.touched.oaId && formik.errors.oaId)}
                  helperText={formik.touched.oaId && formik.errors.oaId}
                  sx={{ mb: 3 }}
                />
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Box sx={{ display: "flex", justifyContent: "center" }}>
                <LogoUpload
                  onLogoChange={handleLogoChange}
                  setShopLogo={setShopLogo}
                  shopLogo={shopLogo}
                  showError={validateLogoError}
                  setShowError={setValidateLogoError}
                  currentLogo={formik?.values?.shopLogo?.link}
                />
              </Box>
            </Grid>

            <Grid item xs={12}>
              <Typography sx={{ fontSize: 15, fontWeight: 500, mb: 0.5 }}>
                Thông tin giới thiệu
              </Typography>
              <ReactQuillEditor
                value={formik.values.businessInfo || ""}
                onChange={handleEditorChange}
                shopId={storeId}
                defaultGroupId={defaultGroupId}
                error={formik.errors.businessInfo}
              />
            </Grid>
          </Grid>
          {isGranted(pathname, PERMISSION_TYPE_ENUM.Edit) && (
            <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 3, gap: 2 }}>
              <Button
                sx={{ color: "#2654FE", borderColor: "#2654FE", textTransform: "none" }}
                variant="outlined"
                onClick={formik.handleReset}
                disabled={isLoading}
              >
                Hủy thay đổi
              </Button>
              <Button
                sx={{ background: "#2654FE", textTransform: "none" }}
                variant="contained"
                onClick={() => formik.handleSubmit()}
              >
                {isLoading ? <CircularProgress size={24} /> : "Lưu thay đổi"}
              </Button>
            </Box>
          )}
        </Card>

        <ShareStoreDialog
          open={shareDialogOpen}
          onClose={() => setShareDialogOpen(false)}
          storeUrl={formik.values.shopUrl}
        />
      </Box>
    </DashboardLayout>
  );
};

export default GeneralSettings;
