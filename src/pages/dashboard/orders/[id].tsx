import React, { useEffect, useState } from 'react';
import DashboardLayout from '@/src/layouts/dashboard';
import { Box, Button, Card, Divider, Typography } from '@mui/material';
import Grid from '@mui/material/Grid2';
import OrderItemsBox from '@/src/components/orders/draft/OrderItemsBox';
import CustomerOrderBox from '@/src/components/orders/draft/CustomerOrderBox';
import OrderTotalPaymentSummary from '@/src/components/orders/draft/OrderTotalPaymentSummary';
import DeliveryMethodOrder from '@/src/components/orders/draft/DeliveryMethodOrder';
import PaymentMethodOrder from '@/src/components/orders/draft/PaymentMethodOrder';
import ShippingAddressOrder from '@/src/components/orders/draft/ShippingAddressOrder';
import { formatMoney } from '@/src/utils/format-money';
import useSnackbar from '@/src/hooks/use-snackbar';
import OrderForm from '@/src/components/orders/draft/OrderDraftForm';
import { middleware } from '@/src/middleware';
import { useRouter } from 'next/router';
import { paths } from '@/src/paths';
import { useOrder } from '@/src/api/hooks/order/use-order';
import { useStoreId } from '@/src/hooks/use-store-id';
import OrderDetail from '@/src/components/orders/OrderDetail';

export default function orderid() {
  const router = useRouter();
  const { getOrder } = useOrder();
  const storeId = useStoreId();
  const [order, setOrder] = useState(null);
  const { id } = router.query;

  const fetchCart = async (id, storeId) => {
    try {
      const response = await getOrder({ orderId: id, shopId: storeId });
      if (response?.data) {
        const { data } = response;
        setOrder(data);
      } else {
        // await router.push(paths.orders.list);
      }
    } catch (error) {
      console.log('🚀 ~ fetchCart ~ error:', error);
    }
  };
  useEffect(() => {
    if (!id) return; // Đảm bảo `id` đã sẵn sàng
    if (!storeId) return; //
    fetchCart(id, storeId);
  }, [id, storeId]);

  return <OrderDetail orderData={order} />;
}
