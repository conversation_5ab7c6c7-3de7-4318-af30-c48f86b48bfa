import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TextField,
  InputAdornment,
  Select,
  MenuItem,
  Stack,
  Typography,
  Avatar,
  Button,
  Tooltip,
} from "@mui/material";
import { Search as SearchIcon } from "@mui/icons-material";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DateRangePicker } from "@mui/x-date-pickers-pro/DateRangePicker";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import dayjs from "dayjs";
import TablePagination from "@mui/material/TablePagination";
import { useMembershipLevel } from "@/src/api/hooks/membership-level/use-membership-level";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useSnackbar } from "notistack";
import { ExchangeHistoryType } from "@/src/api/types/membership.types";
import UserPointDetail from "./PointDetails";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import "dayjs/locale/vi"; // Đảm bảo rằng locale Việt Nam được sử dụng để hiển thị đúng định dạng DD/MM/YYYY
import _ from "lodash";
import { rowPerPageOptionsDefault } from "@/src/types/store/enum-type";
import { useDebounce } from "@/src/hooks/use-debounce";
import TruncatedText from "@/src/components/truncated-text/truncated-text";

const HistoryList = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("ALL");
  const [dateRange, setDateRange] = useState([dayjs().subtract(7, "day"), dayjs()]);
  const [open, setOpen] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [transactions, setTransactions] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const { getExchangeHistory } = useMembershipLevel();
  const [viewDetail, setViewDetail] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const storeId = useStoreId();
  const { enqueueSnackbar } = useSnackbar();
  const debounceValue = useDebounce(searchTerm, 500);

  const getTypeDisplay = (type) => {
    return ExchangeHistoryType[type] || type;
  };

  const fetchHistoryData = async (
    currentPage,
    pageSize,
    shopId,
    filterTypeUpdated,
    FromDate,
    ToDate,
    search
  ) => {
    try {
      setLoading(true);
      setTransactions([]);

      const fromDate = FromDate?.isValid() ? FromDate.toDate() : null;
      const toDate = ToDate?.isValid() ? ToDate.toDate() : null;

      const response = await getExchangeHistory({
        skip: currentPage * pageSize,
        limit: pageSize,
        shopId: shopId,
        search,
        type: filterTypeUpdated,
        fromDate: fromDate,
        toDate: toDate,
      });
      if (response?.data && response.data.data.length > 0) {
        const formattedTransactions = response.data.data.map((item) => ({
          id: item.transactionId,
          type: getTypeDisplay(item.type),
          detail: item.detail,
          user: item.fullName,
          time: dayjs(item.created).format("HH:mm:ss DD/MM/YYYY"),
          value: item.isAdditionEnabled ? `+${item.pointsEarned}` : `${item.pointsEarned}`,
          note: item.note,
          avatar: item.avatar || "",
          isAdd: item.isAdditionEnabled,
          userId: item.userId,
        }));
        setTransactions(formattedTransactions);
        setTotalCount(response.data.total);
      } else {
        setTransactions([]);
        setTotalCount(0);
      }
    } catch (error) {
      console.error("Error fetching history:", error);
    } finally {
      setLoading(false);
    }
  };

  const debouncedFetchHistoryData = useCallback(
    _.debounce((currentPage, pageSize, shopId, filterTypeUpdated, fromDate, toDate, search) => {
      fetchHistoryData(currentPage, pageSize, shopId, filterTypeUpdated, fromDate, toDate, search);
    }, 400),
    []
  );

  useEffect(() => {
    if (!storeId) return;
    debouncedFetchHistoryData(
      page,
      rowsPerPage,
      storeId,
      filterType,
      dateRange[0],
      dateRange[1],
      debounceValue
    );
    return () => {
      debouncedFetchHistoryData.cancel();
    };
  }, [storeId, page, rowsPerPage, filterType, debounceValue, dateRange]);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleFilterChange = (newValue) => {
    setFilterType(newValue);
  };

  const handleViewDetail = (transaction) => {
    const userSelected = {
      id: transaction.userId,
      shopId: storeId,
    };
    setSelectedUser(userSelected);
    setViewDetail(true);
  };
  const handleBack = () => {
    setViewDetail(false);
    setSelectedUser(null);
  };

  const handleStartDateChange = (newValue) => {
    // Nếu ngày bắt đầu mới lớn hơn ngày kết thúc hiện tại, thì điều chỉnh ngày kết thúc
    if (newValue.isAfter(dateRange[1])) {
      setDateRange([newValue, newValue]); // Cập nhật ngày kết thúc thành ngày bắt đầu mới
    } else {
      setDateRange([newValue, dateRange[1]]); // Chỉ cập nhật ngày bắt đầu, giữ nguyên ngày kết thúc
    }
  };

  const handleEndDateChange = (newValue) => {
    // Nếu ngày kết thúc mới nhỏ hơn ngày bắt đầu, thì điều chỉnh ngày bắt đầu
    if (newValue.isBefore(dateRange[0], "day")) {
      setDateRange([newValue, newValue]); // Cập nhật ngày bắt đầu thành ngày kết thúc mới
    } else {
      setDateRange([dateRange[0], newValue]); // Chỉ cập nhật ngày kết thúc, giữ nguyên ngày bắt đầu
    }
  };

  return (
    <Box>
      {viewDetail ? (
        <UserPointDetail user={selectedUser} onBack={handleBack} />
      ) : (
        <Box sx={{ width: "100%", p: 3, backgroundColor: "#fff", borderRadius: 2 }}>
          <Stack direction={"row"} gap={2} mb={2}>
            <TextField
              fullWidth
              placeholder="Tìm kiếm sản phẩm, thông tin khách hàng"
              variant="outlined"
              value={searchTerm}
              onChange={(e) => {
                setPage(0);
                setSearchTerm(e.target.value);
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ backgroundColor: "white", borderRadius: 1 }}
            />

            <Select
              value={filterType}
              onChange={(e) => handleFilterChange(e.target.value)}
              displayEmpty
              sx={{ minWidth: 150, backgroundColor: "white", borderRadius: 1 }}
            >
              {Object.entries(ExchangeHistoryType).map(([key, value]) => (
                <MenuItem key={key} value={key}>
                  {value}
                </MenuItem>
              ))}
            </Select>
            <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="vi">
              <Box sx={{ display: "flex", gap: 2 }}>
                <DatePicker
                  value={dateRange[0]}
                  format="DD/MM/YYYY"
                  onChange={handleStartDateChange}
                  // renderInput={(params) => <TextField {...params} />}
                />
                <DatePicker
                  value={dateRange[1]}
                  format="DD/MM/YYYY"
                  onChange={handleEndDateChange}
                  shouldDisableDate={(date) => date.isBefore(dateRange[0], "day")}
                  // renderInput={(params) => <TextField {...params} />}
                />
              </Box>
            </LocalizationProvider>
          </Stack>

          <TableContainer component={Paper} sx={{ boxShadow: 2, borderRadius: 2 }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ width: 5 }}>STT</TableCell>
                  <TableCell sx={{ width: 300 }}>
                    <b>Khách hàng</b>
                  </TableCell>
                  <TableCell sx={{ width: 150 }}>
                    <b>Thời gian</b>
                  </TableCell>
                  <TableCell sx={{ width: 100 }}>
                    <b>Loại giao dịch</b>
                  </TableCell>

                  <TableCell sx={{ width: 60 }}>
                    <b>Giá trị</b>
                  </TableCell>
                  <TableCell sx={{ width: 300 }}>
                    <b>Chi tiết</b>
                  </TableCell>
                  <TableCell sx={{ width: 300 }}>
                    <b>Ghi chú</b>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {transactions.length > 0 ? (
                  transactions.map((transaction, index) => (
                    <TableRow key={transaction.id}>
                      <TableCell>{page * rowsPerPage + index + 1}</TableCell>
                      <TableCell onClick={() => handleViewDetail(transaction)}>
                        <Stack direction={"row"} alignItems={"center"} gap={1}>
                          <Avatar sx={{ bgcolor: "#1976d2" }} src={transaction.avatar}>
                            {transaction.user}
                          </Avatar>
                          <TruncatedText
                            text={transaction.user}
                            isLink={true}
                            typographyProps={{ color: "primary", fontWeight: 500 }}
                          />
                        </Stack>
                      </TableCell>
                      <TableCell>{transaction?.time}</TableCell>
                      <TableCell>{transaction.type}</TableCell>

                      <TableCell sx={{ color: transaction.isAdd == true ? "green" : "red" }}>
                        {transaction.value}
                      </TableCell>
                      <TableCell>
                        <Tooltip title={transaction.detail}>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              minHeight: "42px",
                            }}
                          >
                            <Typography
                              sx={{
                                display: "-webkit-box",
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: "vertical",
                                overflow: "hidden",
                                fontSize: "14px",
                                width: "100%",
                              }}
                            >
                              {transaction.detail}
                            </Typography>
                          </Box>
                        </Tooltip>
                      </TableCell>
                      <TableCell>
                        <Tooltip title={transaction.note}>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              minHeight: "42px",
                            }}
                          >
                            <Typography
                              sx={{
                                display: "-webkit-box",
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: "vertical",
                                overflow: "hidden",
                                fontSize: "14px",
                                width: "100%",
                              }}
                            >
                              {transaction.note}
                            </Typography>
                          </Box>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      <Stack
                        direction={"column"}
                        alignItems={"center"}
                        justifyContent={"center"}
                        py={4}
                      >
                        <img
                          src="https://cdn-icons-png.flaticon.com/512/2748/2748558.png"
                          alt="No Data"
                          width={100}
                          height={100}
                        />
                        <Typography variant="h6" color="gray">
                          Không có nội dung
                        </Typography>
                      </Stack>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
            <TablePagination
              rowsPerPageOptions={rowPerPageOptionsDefault}
              component="div"
              labelRowsPerPage="Số hàng mỗi trang:"
              count={totalCount}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
              sx={{ width: "100%" }}
            />
          </TableContainer>
        </Box>
      )}
    </Box>
  );
};

export default HistoryList;
