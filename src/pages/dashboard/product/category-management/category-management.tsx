import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";
import { useSnackbar } from "@/src/hooks/use-snackbar";
import { useStoreId } from "@/src/hooks/use-store-id";
import {
  Box,
  Button,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Tooltip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  useTheme,
  useMediaQuery,
  Card,
  Checkbox,
} from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import { tokens } from "@/src/locales/tokens";
import { EmptyState } from "@/src/components/common/empty-state";
import { LoadingState } from "@/src/components/common/loading-state";
import { useProductCategory } from "@/src/api/hooks/dashboard/product/use-category";
import DashboardLayout from "@/src/layouts/dashboard";
import ShowImage from "@/src/components/show-image";
import { paths } from "@/src/paths";
import { StorageService } from "nextjs-api-lib";
import { SearchToolbar } from "../../../../components/components/search-toolbar";
import { HomeDisplayChip } from "../../../../components/components/home-display-chip";
import { StatusChip } from "../../../../components/components/status-chip";
import TitleTypography from "@/src/components/title-typography/title-typography";
import { rowPerPageOptionsDefault } from "@/src/types/store/enum-type";
import { Padding } from "@/src/styles/CommonStyle";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { usePathname } from "next/navigation";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import { useDebounce } from "@/src/hooks/use-debounce";
import { GetProductCategoryRequest } from "@/src/api/types/product-category.types";
import { CategoryDto } from "@/src/api/services/dashboard/product/category.service";
import ZoomImage from "@/src/components/zoom-image";
import ZoomImageDialog from "@/src/components/zoom-image";
import TruncatedText from "@/src/components/truncated-text/truncated-text";

const TAB_STORAGE_KEY = "categoryManagementTab";

const CategoryManagement = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const snackbar = useSnackbar();
  const router = useRouter();
  const pathname = usePathname();
  const { getProductCategory, deleteProductCategory, loading } = useProductCategory();
  const [categories, setCategories] = useState<CategoryDto[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");
  const [openDialog, setOpenDialog] = useState(false);
  const [categoryToDelete, setCategoryToDelete] = useState(null);
  const [openZoom, setOpenZoom] = useState<boolean>(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const debouncedSearchValue = useDebounce(searchTerm, 500);
  const storeId = useStoreId();
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [selected, setSelected] = useState<string[]>([]);
  const [openMultipleDeleteDialog, setOpenMultipleDeleteDialog] = useState(false);
  const { permissions } = useAllPermissions();
  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };
  useEffect(() => {
    const { tab } = router.query;
    if (tab) {
      const index = parseInt(tab as string, 10);
      if (!isNaN(index) && index >= 0 && index <= 1) {
        setTabValue(index);
        localStorage.setItem(TAB_STORAGE_KEY, index.toString());
      }
    }
  }, [router.query]);

  useEffect(() => {
    if (storeId) {
      setIsInitialLoading(true);
      fetchCategories().finally(() => {
        setIsInitialLoading(false);
      });
    }
  }, [page, rowsPerPage, debouncedSearchValue, storeId, tabValue]);

  const fetchCategories = async () => {
    try {
      const categoryType = tabValue === 0 ? "Product" : "Service";
      const params: GetProductCategoryRequest = {
        categoryType,
        shopId: storeId,
        partnerId: StorageService.get("partnerId") as string,
        paging: {
          pageIndex: page,
          pageSize: rowsPerPage,
          search: debouncedSearchValue,
          nameType: "Created",
          sortType: "Desc",
          name: "Created",
          sort: "Desc",
        },
      };
      const response = await getProductCategory(params);
      setCategories(response?.data?.data || []);
      setTotalCount(response?.data?.total || 0);
    } catch (error) {}
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    setSelected([]);
    localStorage.setItem(TAB_STORAGE_KEY, newValue.toString());
    router.push(
      {
        pathname: router.pathname,
        query: { ...router.query, tab: newValue },
      },
      undefined,
      { shallow: true }
    );
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleCreate = () => {
    const categoryType = tabValue === 0 ? "Product" : "Service";
    StorageService.set("categoryType", categoryType);
    router.push(paths.dashboard.product.createProductCategory);
  };

  const handleEdit = (category) => {
    const categoryType = tabValue === 0 ? "Product" : "Service";
    StorageService.set("categoryType", categoryType);
    StorageService.set("editCategoryData", category);
    router.push({
      pathname: paths.dashboard.product.createProductCategory,
      query: { id: category.categoryId },
    });
  };

  const handleDelete = (category: any) => {
    setCategoryToDelete(category);
    setOpenDialog(true);
  };

  const confirmDelete = async () => {
    try {
      if (categoryToDelete?.quantityItems === 0) {
        const res = await deleteProductCategory(categoryToDelete.categoryId);
        if (res?.status === 200) {
          snackbar.success("Xóa danh mục thành công");

          if (selected.includes(categoryToDelete.categoryId)) {
            setSelected(selected.filter((id) => id !== categoryToDelete.categoryId));
          }

          setOpenDialog(false);
          setCategoryToDelete(null);
          fetchCategories();
        }
      } else {
        snackbar.warning("Danh mục này đang được áp dụng cho sản phẩm");
      }
    } catch (error) {
      snackbar.error("Xóa danh mục thất bại");
    }
  };

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      setSelected(categories.map((category) => category.categoryId));
    } else {
      setSelected([]);
    }
  };

  const handleSelectOne = (event: React.ChangeEvent<HTMLInputElement>, id: string) => {
    if (event.target.checked) {
      setSelected([...selected, id]);
    } else {
      setSelected(selected.filter((item) => item !== id));
    }
  };

  const handleDeleteMultiple = () => {
    setOpenMultipleDeleteDialog(true);
  };

  const confirmDeleteMultiple = async () => {
    try {
      await Promise.all(selected.map((id) => deleteProductCategory(id)));
      setPage(0);
      setRowsPerPage(10);
      snackbar.success(`Xóa ${selected.length} danh mục thành công`);
      setSelected([]);
      setOpenMultipleDeleteDialog(false);
      fetchCategories();
    } catch (error) {
      snackbar.error("Xóa danh mục thất bại");
    }
  };

  // get Title by tab index
  const getTitleByTab = (tabIndex: number) => {
    return tabIndex === 0
      ? "Hàng hoá/Danh mục/Danh mục sản phẩm"
      : "Hàng hoá/Danh mục/Danh mục dịch vụ";
  };

  return (
    <DashboardLayout>
      <Box sx={{ p: Padding, paddingTop: "20px" }}>
        <TitleTypography
          sx={{
            fontSize: "20px !important",
            lineHeight: "1",
            fontWeight: "700",
            paddingBottom: "20px",
            marginBottom: "16px",
            borderBottom: "1px solid #bdbdbd",
          }}
        >
          {getTitleByTab(tabValue)}
        </TitleTypography>
        <Card sx={{ p: 3 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            sx={{
              mb: 3,
              "& .MuiTabs-indicator": {
                background: "#2654FE",
              },
            }}
          >
            <Tab
              sx={{
                textTransform: "none !important",
                color: "#000",
                "&.Mui-selected": {
                  color: "#2654FE !important",
                  height: "3px",
                },
              }}
              label={t(tokens.contentManagement.category.tabs.product)}
            />
            <Tab
              sx={{
                textTransform: "none !important",
                color: "#000",
                "&.Mui-selected": {
                  color: "#2654FE !important",
                  height: "3px",
                },
              }}
              label={t(tokens.contentManagement.category.tabs.service)}
            />
          </Tabs>

          <SearchToolbar
            isGranted={isGranted}
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            onAddClick={handleCreate}
            onDeleteClick={selected.length > 0 ? handleDeleteMultiple : undefined}
            selectedCount={selected.length}
            placeholder={t(tokens.contentManagement.category.search.placeholder)}
            addButtonText={t(tokens.contentManagement.category.actions.create)}
            tabValue={tabValue}
            pageType="category"
            listItem={categories}
            fetchData={fetchCategories}
          />

          {(loading && page === 0) || isInitialLoading ? (
            <LoadingState />
          ) : !isInitialLoading && categories.length === 0 ? (
            <EmptyState
              title={t(tokens.contentManagement.category.emptyState.title)}
              subtitle={t(tokens.contentManagement.category.emptyState.subtitle)}
              buttonText={t(tokens.contentManagement.category.emptyState.button)}
              onAddClick={handleCreate}
              isGranted={isGranted}
            />
          ) : (
            <Card>
              <Box
                sx={{
                  width: "100%",
                  overflowX: "auto",
                }}
              >
                <Table sx={{ minWidth: 850 }}>
                  <TableHead>
                    <TableRow>
                      <TableCell padding="checkbox">
                        <Checkbox
                          checked={categories.length > 0 && selected.length === categories.length}
                          indeterminate={selected.length > 0 && selected.length < categories.length}
                          onChange={handleSelectAll}
                        />
                      </TableCell>
                      <TableCell>STT</TableCell>
                      <TableCell
                        sx={{
                          minWidth: "80px",
                          whiteSpace: "nowrap",
                        }}
                      >
                        {t(tokens.contentManagement.category.table.image)}
                      </TableCell>
                      <TableCell
                        sx={{
                          minWidth: "200px",
                        }}
                      >
                        {t(tokens.contentManagement.category.table.name)}
                      </TableCell>
                      <TableCell
                        sx={{
                          minWidth: "150px",
                        }}
                      >
                        {t(tokens.contentManagement.category.table.parentCategory)}
                      </TableCell>
                      <TableCell
                        align="center"
                        sx={{
                          minWidth: "100px",
                          whiteSpace: "nowrap",
                        }}
                      >
                        {t(tokens.contentManagement.category.table.productCount)}
                      </TableCell>
                      <TableCell
                        align="center"
                        sx={{
                          minWidth: "100px",
                          whiteSpace: "nowrap",
                        }}
                      >
                        {t(tokens.contentManagement.category.table.displayOrder)}
                      </TableCell>
                      {/* <TableCell
                        align="center"
                        sx={{
                          minWidth: '120px',
                          whiteSpace: 'nowrap'
                        }}
                      >
                        {t(tokens.contentManagement.category.table.homeDisplay)}
                      </TableCell> */}
                      <TableCell
                        align="center"
                        sx={{
                          minWidth: "100px",
                          whiteSpace: "nowrap",
                        }}
                      >
                        {t(tokens.contentManagement.category.table.status)}
                      </TableCell>
                      <TableCell
                        align="center"
                        sx={{
                          minWidth: "100px",
                          whiteSpace: "nowrap",
                        }}
                      >
                        {t(tokens.contentManagement.category.table.actions)}
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {categories.map((category, index) => (
                      <TableRow
                        key={category.categoryId}
                        sx={{
                          "&:nth-of-type(odd)": {
                            backgroundColor: theme.palette.action.hover,
                          },
                        }}
                      >
                        <TableCell padding="checkbox">
                          <Checkbox
                            checked={selected.includes(category.categoryId)}
                            onChange={(event) => {
                              if (category?.quantityItems === 0) {
                                handleSelectOne(event, category.categoryId);
                              } else {
                                snackbar.warning("Danh mục này đã được áp dụng cho sản phẩm");
                              }
                            }}
                          />
                        </TableCell>
                        <TableCell>{page * rowsPerPage + index + 1}</TableCell>
                        <TableCell>
                          <Box
                            sx={{
                              width: 40,
                              height: 40,
                              overflow: "hidden",
                              borderRadius: 1,
                              boxShadow: 3,
                              flexShrink: 0,
                              cursor: category?.image?.link ? "pointer" : "default",
                              "&:hover": {
                                opacity: 0.8,
                              },
                            }}
                            onClick={() => {
                              if (!category?.image?.link) {
                                return;
                              }
                              setSelectedImage(category?.image?.link);
                              setOpenZoom(true);
                            }}
                          >
                            <img
                              src={category?.image?.link}
                              alt={category.categoryName}
                              style={{
                                width: "100%",
                                height: "100%",
                                objectFit: "cover",
                              }}
                            />
                          </Box>
                        </TableCell>
                        <TableCell>
                          <TruncatedText
                            typographyProps={{ width: 350 }}
                            text={category.categoryName}
                          />
                        </TableCell>
                        <TableCell>
                          <TruncatedText
                            typographyProps={{ width: 300 }}
                            text={category.parentName}
                          />
                        </TableCell>
                        <TableCell align="center">{category.quantityItems || 0}</TableCell>
                        <TableCell align="center">{category.categoryPosition}</TableCell>
                        {/* <TableCell align="center">{<HomeDisplayChip isHome={category.isHome} />}</TableCell> */}
                        <TableCell align="center">
                          {
                            <StatusChip
                              status={
                                category.publish as
                                  | "Actived"
                                  | "InActived"
                                  | "Publish"
                                  | "UnPublish"
                              }
                            />
                          }
                        </TableCell>
                        <TableCell align="center">
                          <Box sx={{ display: "flex", justifyContent: "center", gap: 1 }}>
                            <Tooltip
                              title={
                                !isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)
                                  ? "Bạn không có quyền sửa"
                                  : "Sửa danh mục"
                              }
                            >
                              <span>
                                <IconButton
                                  onClick={() => handleEdit(category)}
                                  size="small"
                                  sx={{ color: "primary.main" }}
                                  disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Edit)}
                                >
                                  <EditIcon fontSize="small" />
                                </IconButton>
                              </span>
                            </Tooltip>
                            <Tooltip
                              title={
                                !isGranted(pathname, PERMISSION_TYPE_ENUM.Delete)
                                  ? "Bạn không có quyền xoá"
                                  : "Xoá danh mục"
                              }
                            >
                              <span>
                                <IconButton
                                  onClick={() => handleDelete(category)}
                                  size="small"
                                  sx={{ color: "error.main" }}
                                  disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Delete)}
                                >
                                  <DeleteIcon fontSize="small" />
                                </IconButton>
                              </span>
                            </Tooltip>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </Box>
              <TablePagination
                labelRowsPerPage="Số hàng mỗi trang"
                component="div"
                count={totalCount}
                page={page}
                onPageChange={handleChangePage}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                rowsPerPageOptions={rowPerPageOptionsDefault}
              />
            </Card>
          )}
        </Card>

        <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
          <DialogTitle>{t(tokens.contentManagement.category.delete.confirmTitle)}</DialogTitle>
          <DialogContent>
            <DialogContentText>
              {t(tokens.contentManagement.category.delete.confirmMessage, {
                name: categoryToDelete?.categoryName,
              })}
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenDialog(false)} variant="outlined">
              {t(tokens.common.cancel)}
            </Button>
            <Button onClick={confirmDelete} color="error" variant="contained" autoFocus>
              {t(tokens.common.delete)}
            </Button>
          </DialogActions>
        </Dialog>

        <Dialog
          open={openMultipleDeleteDialog}
          onClose={() => setOpenMultipleDeleteDialog(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Xác nhận xóa nhiều danh mục</DialogTitle>
          <DialogContent>
            <DialogContentText>
              {`Bạn có chắc chắn muốn xóa ${selected.length} danh mục đã chọn không?`}
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => setOpenMultipleDeleteDialog(false)}
              variant="outlined"
              sx={{ minWidth: 100 }}
            >
              Hủy
            </Button>
            <Button
              onClick={confirmDeleteMultiple}
              color="error"
              variant="contained"
              autoFocus
              sx={{ minWidth: 100 }}
            >
              Xóa
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
      <ZoomImageDialog
        onClose={() => {
          setSelectedImage(null);
          setOpenZoom(false);
        }}
        open={openZoom}
        src={selectedImage || ""}
        alt={"Zoomed category image"}
        maxHeight="80vh"
      />
    </DashboardLayout>
  );
};

export default CategoryManagement;
