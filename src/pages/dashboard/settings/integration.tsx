import React, { useEffect, useState } from "react";
import SettingLayout from "@/src/components/settings/settings-page/SettingLayout";
import {
  Box,
  Paper,
  Typography,
  Button,
  Grid,
  Avatar,
  Stack,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider,
  Chip,
} from "@mui/material";
import IntegrationInstructionsIcon from "@mui/icons-material/IntegrationInstructions";
import StoreIcon from "@mui/icons-material/Store";
import InfoIcon from "@mui/icons-material/Info";
import { useRouter } from "next/router";
import IntegrationForm, { IntegrationField } from "./components/IntegrationForm";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useSyncService } from "@/src/api/hooks/sync-service/sync-service";
export const SyncService = {
  Sapo: "Sapo",
  KiotViet: "KiotViet",
  NhanhVN: "NhanhVN",
  Odoo: "Odoo",
};
const integrations = [
  {
    key: "nhanh",
    name: "Nhanh.vn",
    description: "<PERSON><PERSON>t n<PERSON>i với <PERSON>.vn để đồng bộ đơn hàng, tồ<PERSON> kho, ...",
    icon: <img style={{ borderRadius: "50%" }} src="/assets/nhanh.png" alt="nhanh" />,
    color: "#e3f2fd",
    fields: [
      { name: "appId", label: "App ID", placeholder: "Nhập App ID", required: true },
      {
        name: "secretKey",
        label: "Secret Key",
        placeholder: "Nhập Secret Key",
        type: "password",
        required: true,
      },
    ],
  },
  {
    key: "kiotviet",
    name: "KiotViet",
    description: "Kết nối với KiotViet để đồng bộ dữ liệu bán hàng.",
    icon: <StoreIcon sx={{ fontSize: 40, color: "#43a047" }} />,
    color: "#e8f5e9",
    fields: [
      { name: "clientId", label: "Client ID", placeholder: "Nhập Client ID", required: true },
      {
        name: "clientSecret",
        label: "Client Secret",
        placeholder: "Nhập Client Secret",
        type: "password",
        required: true,
      },
    ],
  },
];

const mockConnected = {
  nhanh: false,
  kiotviet: false,
};

const LeftColumn = ({ children }) => (
  <Grid item xs={12} md={4}>
    <Box>{children}</Box>
  </Grid>
);

const RightColumn = ({ children }) => (
  <Grid item xs={12} md={8} sx={{ padding: 2 }}>
    <Box>{children}</Box>
  </Grid>
);

const IntegrationList: React.FC = () => {
  const storeId = useStoreId();
  const [openDialog, setOpenDialog] = useState<string | null>(null);
  const [connected, setConnected] = useState(mockConnected);
  const [openDevDialog, setOpenDevDialog] = useState(false);
  const [openDetailDialog, setOpenDetailDialog] = useState(false);
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const {
    configSyncService,
    getSyncServiceConfig,
    updateAccessCode,
    deleteSyncServiceConfig,
    loading,
  } = useSyncService();
  const [nhanhConfig, setNhanhConfig] = useState<Record<string, string> | null>(null);
  const router = useRouter();

  useEffect(() => {
    const { accessCode } = router.query;
    if (accessCode && storeId) {
      (async () => {
        try {
          await updateAccessCode({ accessCode, ShopId: storeId, syncService: SyncService.NhanhVN });
          await fetchNhanhConfig();
        } catch (error) {
          console.error("Lưu accessCode thất bại:", error);
        } finally {
          const { accessCode, ...restQuery } = router.query;
          router.replace({ pathname: router.pathname, query: restQuery }, undefined, {
            shallow: true,
          });
        }
      })();
    }
  }, [router.query.accessCode, storeId]);

  useEffect(() => {
    if (storeId) fetchNhanhConfig();
  }, [storeId]);

  const fetchNhanhConfig = async () => {
    try {
      const response = await getSyncServiceConfig({
        shopId: storeId,
        syncService: SyncService.NhanhVN,
      });
      console.log(response);
      if (response.data.result && response.data.result.status == "Actived") {
        setConnected((prev) => ({
          ...prev,
          nhanh: !!response?.data.result,
        }));
        setNhanhConfig({
          appId: response.data.result.appId || "",
          secretKey: response.data.result.secretKey || "",
          verifyToken: response.data.result.verifyToken || "",
        });
      } else {
        setNhanhConfig(null);
      }
    } catch (error) {
      console.error("Error fetching groups:", error);
    }
  };
  const handleOpenDialog = (key: string) => {
    if (key === "kiotviet") {
      setOpenDevDialog(true);
    } else if (key === "nhanh" && connected.nhanh) {
      setOpenDetailDialog(true);
    } else {
      setOpenDialog(key);
    }
  };
  const handleCloseDialog = () => setOpenDialog(null);
  const handleCloseDevDialog = () => setOpenDevDialog(false);
  const handleCloseDetailDialog = () => setOpenDetailDialog(false);

  const handleSubmit = async (key: string, values: Record<string, string>) => {
    if (key === "nhanh") {
      try {
        const response = await configSyncService({
          ...values,
          ShopId: storeId,
          syncService: SyncService.NhanhVN,
        });
        if (response.data.result.data) {
          const appId = response.data.result.data.appId;
          const returnLink = `${window.location.origin}/dashboard/settings/integration`;
          const oauthUrl = `https://nhanh.vn/oauth?version=2.0&appId=${appId}&returnLink=${encodeURIComponent(
            returnLink
          )}`;
          window.location.href = oauthUrl;
        }
      } catch (error) {
        console.error(error);
      }
    } else {
      setConnected((prev) => ({ ...prev, [key]: true }));
      setOpenDialog(null);
    }
  };

  const handleDisconnectNhanh = () => {
    setOpenConfirmDialog(true);
  };

  const handleConfirmDisconnect = async () => {
    try {
      await deleteSyncServiceConfig({
        shopId: storeId,
        syncService: SyncService.NhanhVN,
      });
      setConnected((prev) => ({ ...prev, nhanh: false }));
      setNhanhConfig(null);
      setOpenDetailDialog(false);
      setOpenConfirmDialog(false);
    } catch (error) {
      console.error("Lỗi khi ngừng kết nối:", error);
    }
  };

  return (
    <SettingLayout>
      <Grid container spacing={4}>
        <LeftColumn>
          <Typography fontWeight="bold" marginBottom={2}>
            Dịch vụ tích hợp
          </Typography>
          <Typography variant="subtitle2" color="text.secondary">
            Kết nối các dịch vụ bên thứ ba để đồng bộ dữ liệu, đơn hàng, tồn kho và nhiều hơn nữa.
            Chọn dịch vụ bên phải để thiết lập kết nối.
          </Typography>
        </LeftColumn>
        <RightColumn>
          <Typography fontWeight="bold" mb={1}>
            Danh sách dịch vụ tích hợp
          </Typography>
          <Box display="flex" flexDirection="column" gap={2}>
            {integrations.map((item) => {
              const isConnected = connected[item.key];
              return (
                <Paper
                  key={item.key}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    p: 2,
                    border: "1px solid #e0e0e0",
                    boxShadow: 0,
                    background: item.color,
                  }}
                >
                  <Box
                    sx={{
                      width: 48,
                      height: 48,
                      borderRadius: 2,
                      border: `3px solid #e0e0e0`,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      mr: 2,
                      background: "#f5f5f5",
                    }}
                  >
                    {item.icon}
                  </Box>
                  <Box flex={1} minWidth={0}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <Typography fontWeight={700}>{item.name}</Typography>
                      <Box
                        sx={{
                          backgroundColor: isConnected ? "#00ce00" : "#f1c232",
                          color: "#fff",
                          borderRadius: 1,
                          px: 1,
                          fontSize: 12,
                          fontWeight: 600,
                          lineHeight: "20px",
                          display: "inline-block",
                        }}
                      >
                        {isConnected ? "Đã kết nối" : "Chưa kết nối"}
                      </Box>
                    </Box>
                    <Typography variant="body2" color="text.secondary" noWrap>
                      {item.description}
                    </Typography>
                  </Box>
                  <Button
                    variant="contained"
                    onClick={() => handleOpenDialog(item.key)}
                    sx={{ fontWeight: 600, minWidth: 140, ml: 2 }}
                  >
                    {isConnected ? "Xem chi tiết" : "Thiết lập"}
                  </Button>
                  {item.key !== "kiotviet" && (
                    <Dialog
                      open={openDialog === item.key}
                      onClose={handleCloseDialog}
                      maxWidth="xs"
                      fullWidth
                      PaperProps={{
                        sx: {
                          borderRadius: 4,
                          boxShadow: "0 8px 32px 0 rgba(25, 118, 210, 0.15)",
                          p: 0,
                          overflow: "visible",
                        },
                      }}
                    >
                      <Box
                        sx={{
                          p: { xs: 2, sm: 4 },
                          pt: 0,
                          bgcolor: "#f8fbff",
                          borderRadius: 4,
                          minWidth: { xs: 320, sm: 400 },
                          maxWidth: 480,
                          position: "relative",
                        }}
                      >
                        <Box
                          display="flex"
                          flexDirection="column"
                          alignItems="center"
                          mt={-6}
                          mb={2}
                        >
                          <Box
                            sx={{
                              width: 72,
                              height: 72,
                              borderRadius: "50%",
                              bgcolor: "#fff",
                              boxShadow: "0 2px 8px 0 rgba(25, 118, 210, 0.10)",
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              p: 1,
                              mb: 1,
                            }}
                          >
                            {item.icon}
                          </Box>
                          <Typography variant="h5" fontWeight={700} color="#1976d2" align="center">
                            {`Kết nối ${item.name}`}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" align="center" mt={1}>
                            {item.description}
                          </Typography>
                        </Box>
                        <IntegrationForm
                          title=""
                          fields={item.fields}
                          onSubmit={(values) => handleSubmit(item.key, values)}
                          loading={loading}
                          successMessage={`Lưu thông tin kết nối ${item.name} thành công!`}
                          values={item.key === "nhanh" ? nhanhConfig : undefined}
                        />
                      </Box>
                    </Dialog>
                  )}
                  {item.key === "kiotviet" && (
                    <Dialog
                      open={openDevDialog}
                      onClose={handleCloseDevDialog}
                      maxWidth="xs"
                      fullWidth
                    >
                      <Box p={4} textAlign="center">
                        <Typography variant="h6" fontWeight={700} mb={2}>
                          Tính năng đang phát triển
                        </Typography>
                        <Typography color="text.secondary">
                          Kết nối KiotViet sẽ sớm được ra mắt. Vui lòng quay lại sau!
                        </Typography>
                        <Button onClick={handleCloseDevDialog} sx={{ mt: 3 }} variant="contained">
                          Đóng
                        </Button>
                      </Box>
                    </Dialog>
                  )}
                  {item.key === "nhanh" && (
                    <Dialog
                      open={openDetailDialog}
                      onClose={handleCloseDetailDialog}
                      maxWidth="sm"
                      fullWidth
                      PaperProps={{
                        sx: {
                          borderRadius: 3,
                          boxShadow: "0 8px 32px 0 rgba(25, 118, 210, 0.15)",
                        },
                      }}
                    >
                      <DialogTitle sx={{ pb: 1 }}>
                        <Box display="flex" alignItems="center" gap={2}>
                          <Box
                            sx={{
                              width: 48,
                              height: 48,
                              borderRadius: "50%",
                              bgcolor: "#e3f2fd",
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                            }}
                          >
                            <img
                              style={{ borderRadius: "50%" }}
                              src="/assets/nhanh.png"
                              alt="nhanh"
                              width={32}
                              height={32}
                            />
                          </Box>
                          <Box>
                            <Typography variant="h6" fontWeight={700}>
                              Chi tiết kết nối Nhanh.vn
                            </Typography>
                            <Chip
                              label="Đã kết nối"
                              color="success"
                              size="small"
                              sx={{ mt: 0.5 }}
                            />
                          </Box>
                        </Box>
                      </DialogTitle>
                      <Divider />
                      <DialogContent sx={{ pt: 3 }}>
                        <Box display="flex" flexDirection="column" gap={3}>
                          <Box>
                            <Typography
                              variant="subtitle2"
                              fontWeight={600}
                              color="text.secondary"
                              mb={1}
                            >
                              App ID
                            </Typography>
                            <Typography
                              variant="body1"
                              sx={{
                                bgcolor: "#f5f5f5",
                                p: 1.5,
                                borderRadius: 1,
                                fontFamily: "monospace",
                                wordBreak: "break-all",
                              }}
                            >
                              {nhanhConfig?.appId || "Không có dữ liệu"}
                            </Typography>
                          </Box>

                          <Box>
                            <Typography
                              variant="subtitle2"
                              fontWeight={600}
                              color="text.secondary"
                              mb={1}
                            >
                              Secret Key
                            </Typography>
                            <Typography
                              variant="body1"
                              sx={{
                                bgcolor: "#f5f5f5",
                                p: 1.5,
                                borderRadius: 1,
                                fontFamily: "monospace",
                                wordBreak: "break-all",
                              }}
                            >
                              {"*".repeat(nhanhConfig?.secretKey?.length || 0)}
                            </Typography>
                          </Box>

                          <Box>
                            <Box display="flex" alignItems="center" gap={1} mb={1}>
                              <Typography
                                variant="subtitle2"
                                fontWeight={600}
                                color="text.secondary"
                              >
                                Webhooks Verify Token
                              </Typography>
                              <InfoIcon sx={{ fontSize: 16, color: "text.secondary" }} />
                            </Box>
                            <Typography
                              variant="caption"
                              color="text.secondary"
                              mb={1}
                              display="block"
                            >
                              Token này được sử dụng để xác thực webhook từ Nhanh.vn. Nhập token này
                              vào cấu hình webhook bên Nhanh.
                            </Typography>
                            <Typography
                              variant="body1"
                              sx={{
                                bgcolor: "#f5f5f5",
                                p: 1.5,
                                borderRadius: 1,
                                fontFamily: "monospace",
                                wordBreak: "break-all",
                              }}
                            >
                              {nhanhConfig?.verifyToken || "Chưa có token"}
                            </Typography>
                          </Box>
                        </Box>
                      </DialogContent>
                      <DialogActions sx={{ p: 3, pt: 2, justifyContent: "end" }}>
                        <Box display="flex" gap={1}>
                          <Button onClick={handleCloseDetailDialog} variant="outlined">
                            Đóng
                          </Button>
                          <Button
                            onClick={handleDisconnectNhanh}
                            variant="outlined"
                            color="error"
                            disabled={loading}
                          >
                            Ngừng kết nối
                          </Button>
                        </Box>
                      </DialogActions>
                    </Dialog>
                  )}
                </Paper>
              );
            })}
          </Box>
        </RightColumn>
      </Grid>

      {/* Dialog xác nhận ngừng kết nối */}
      <Dialog
        open={openConfirmDialog}
        onClose={() => setOpenConfirmDialog(false)}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>
          <Typography variant="h6" fontWeight={700}>
            Xác nhận ngừng kết nối
          </Typography>
        </DialogTitle>
        <DialogContent>
          <Typography color="text.secondary">
            Bạn có chắc chắn muốn ngừng kết nối với Nhanh.vn? Tất cả cấu hình và dữ liệu kết nối sẽ
            bị xóa.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ p: 3, pt: 2 }}>
          <Button onClick={() => setOpenConfirmDialog(false)} variant="outlined">
            Hủy
          </Button>
          <Button
            onClick={handleConfirmDisconnect}
            variant="contained"
            color="error"
            disabled={loading}
          >
            Ngừng kết nối
          </Button>
        </DialogActions>
      </Dialog>
    </SettingLayout>
  );
};

export default IntegrationList;
